// ===================================================================
// TESTE COMPLETO DO FLUXO DE AFILIADOS - AFFILIATE FLOW CRAFT
// Testa todo o sistema end-to-end para garantir funcionamento perfeito
// ===================================================================

const fs = require('fs');
const path = require('path');

// Carregar variáveis de ambiente do arquivo .env
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const envVars = {};

      envContent.split('\n').forEach(line => {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').trim();
          envVars[key.trim()] = value;
        }
      });

      return envVars;
    }
  } catch (error) {
    console.log('⚠️  Erro ao carregar .env:', error.message);
  }
  return {};
}

const envVars = loadEnvFile();
console.log('🚀 INICIANDO TESTE COMPLETO DO SISTEMA...\n');

// Simular dados de teste
const testLead = {
  name: 'João Silva',
  email: '<EMAIL>',
  phone: '+5511999999999',
  niche: 'tech',
  source: 'landing-page-test'
};

// Teste 1: Verificar configurações
console.log('📋 TESTE 1: Verificando configurações...');
const requiredEnvVars = [
  'VITE_BREVO_API_KEY',
  'VITE_BREVO_LIST_ID',
  'VITE_BREVO_TEMPLATE_WELCOME'
];

let configOk = true;
requiredEnvVars.forEach(envVar => {
  const value = envVars[envVar];
  if (!value) {
    console.log(`❌ ${envVar} não configurado`);
    configOk = false;
  } else {
    console.log(`✅ ${envVar} configurado`);
  }
});

if (!configOk) {
  console.log('\n❌ ERRO: Configurações incompletas. Verifique o arquivo .env');
  process.exit(1);
}

// Teste 2: Testar API Brevo
console.log('\n📧 TESTE 2: Testando API Brevo...');

async function testBrevoAPI() {
  try {
    const apiKey = envVars.VITE_BREVO_API_KEY || '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';
    
    // Test account info
    const accountResponse = await fetch('https://api.brevo.com/v3/account', {
      headers: {
        'api-key': apiKey,
        'Content-Type': 'application/json'
      }
    });

    if (accountResponse.ok) {
      const accountData = await accountResponse.json();
      console.log('✅ Conexão Brevo OK');
      console.log(`📊 Plano: ${accountData.plan.type}`);
      console.log(`📧 Email: ${accountData.email}`);
    } else {
      console.log('❌ Erro na conexão Brevo:', accountResponse.status);
      return false;
    }

    // Test email sending
    const emailData = {
      sender: { email: '<EMAIL>', name: 'AffiliateFlow Team' },
      to: [{ email: testLead.email, name: testLead.name }],
      subject: '🚀 Teste do Sistema AffiliateFlow',
      htmlContent: `
        <h2>Olá ${testLead.name}!</h2>
        <p>Este é um teste do sistema de email marketing.</p>
        <p><strong>Dados capturados:</strong></p>
        <ul>
          <li>Nome: ${testLead.name}</li>
          <li>Email: ${testLead.email}</li>
          <li>Nicho: ${testLead.niche}</li>
          <li>Fonte: ${testLead.source}</li>
        </ul>
        <p>Sistema funcionando perfeitamente! 🎉</p>
      `,
      tags: ['test', 'system-check']
    };

    const emailResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'api-key': apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    });

    if (emailResponse.ok) {
      const emailResult = await emailResponse.json();
      console.log('✅ Email de teste enviado');
      console.log(`📨 Message ID: ${emailResult.messageId}`);
      return true;
    } else {
      console.log('❌ Erro no envio de email:', emailResponse.status);
      return false;
    }

  } catch (error) {
    console.log('❌ Erro no teste Brevo:', error.message);
    return false;
  }
}

// Teste 3: Validar links de afiliado
console.log('\n🔗 TESTE 3: Validando links de afiliado...');

function testAffiliateLinks() {
  const gripLink = 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368';
  const androidLink = 'https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share';
  const iosLink = 'https://apps.apple.com/us/app/grip-gaiodataos/id6743857628';

  console.log('✅ Link GRIP:', gripLink);
  console.log('✅ Link Android:', androidLink);
  console.log('✅ Link iOS:', iosLink);
  
  return true;
}

// Teste 4: Verificar PDFs
console.log('\n📄 TESTE 4: Verificando materiais PDF...');

function testPDFs() {
  const pdfPaths = [
    './pdf/Domine-a-Nova-Economia-Digital.pdf',
    './public/pdf/Domine-a-Nova-Economia-Digital.pdf'
  ];

  let pdfFound = false;
  pdfPaths.forEach(pdfPath => {
    if (fs.existsSync(pdfPath)) {
      const stats = fs.statSync(pdfPath);
      console.log(`✅ PDF encontrado: ${pdfPath} (${Math.round(stats.size / 1024)}KB)`);
      pdfFound = true;
    }
  });

  if (!pdfFound) {
    console.log('❌ Nenhum PDF encontrado');
    return false;
  }

  return true;
}

// Teste 5: Verificar estrutura de componentes
console.log('\n🧩 TESTE 5: Verificando componentes...');

function testComponents() {
  const componentPaths = [
    './src/components/HeroSection.tsx',
    './src/components/LeadCaptureForm.tsx',
    './src/components/StepContent.tsx',
    './src/services/brevoService.ts',
    './src/services/emailMarketing.ts'
  ];

  let allComponentsOk = true;
  componentPaths.forEach(componentPath => {
    if (fs.existsSync(componentPath)) {
      console.log(`✅ Componente OK: ${componentPath}`);
    } else {
      console.log(`❌ Componente faltando: ${componentPath}`);
      allComponentsOk = false;
    }
  });

  return allComponentsOk;
}

// Executar todos os testes
async function runAllTests() {
  console.log('🔄 Executando todos os testes...\n');

  const results = {
    config: configOk,
    brevo: await testBrevoAPI(),
    links: testAffiliateLinks(),
    pdfs: testPDFs(),
    components: testComponents()
  };

  console.log('\n📊 RESULTADO FINAL:');
  console.log('='.repeat(50));
  
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASSOU' : '❌ FALHOU';
    console.log(`${test.toUpperCase().padEnd(15)} ${status}`);
  });

  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 TODOS OS TESTES PASSARAM!');
    console.log('🚀 Sistema pronto para produção!');
  } else {
    console.log('\n⚠️  ALGUNS TESTES FALHARAM');
    console.log('🔧 Verifique os erros acima e corrija antes do deploy');
  }

  return allPassed;
}

// Executar
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
});
