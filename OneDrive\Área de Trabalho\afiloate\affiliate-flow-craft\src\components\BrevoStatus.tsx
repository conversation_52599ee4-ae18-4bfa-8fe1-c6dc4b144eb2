// ===================================================================
// BREVO STATUS COMPONENT - AffiliateFlow Pro
// Shows real-time status of Brevo integration
// ===================================================================

import React, { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, Mail, Zap, Users } from 'lucide-react';
import { config } from '../core/config';

const BrevoStatus: React.FC = () => {
  const [status, setStatus] = useState<{
    isConfigured: boolean;
    isConnected: boolean;
    accountInfo?: any;
    error?: string;
  }>({
    isConfigured: false,
    isConnected: false
  });

  useEffect(() => {
    checkBrevoStatus();
  }, []);

  const checkBrevoStatus = async () => {
    const isConfigured = !!config.brevo.apiKey && config.brevo.apiKey !== '';
    
    if (!isConfigured) {
      setStatus({
        isConfigured: false,
        isConnected: false,
        error: 'API Key não configurada'
      });
      return;
    }

    try {
      // Test connection to Brevo
      const response = await fetch('https://api.brevo.com/v3/account', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'api-key': config.brevo.apiKey
        }
      });

      if (response.ok) {
        const accountInfo = await response.json();
        setStatus({
          isConfigured: true,
          isConnected: true,
          accountInfo
        });
      } else {
        const errorData = await response.json();
        setStatus({
          isConfigured: true,
          isConnected: false,
          error: errorData.message || 'Erro de conexão'
        });
      }
    } catch (error) {
      setStatus({
        isConfigured: true,
        isConnected: false,
        error: 'Erro de rede'
      });
    }
  };

  if (!status.isConfigured) {
    return (
      <div className="fixed top-4 right-4 bg-orange-100 border border-orange-300 rounded-lg p-4 max-w-sm z-50">
        <div className="flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-orange-600" />
          <div>
            <div className="font-medium text-orange-800">Modo Fallback</div>
            <div className="text-sm text-orange-700">Emails via sistema demo</div>
          </div>
        </div>
      </div>
    );
  }

  if (!status.isConnected) {
    return (
      <div className="fixed top-4 right-4 bg-red-100 border border-red-300 rounded-lg p-4 max-w-sm z-50">
        <div className="flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-600" />
          <div>
            <div className="font-medium text-red-800">Erro Brevo</div>
            <div className="text-sm text-red-700">{status.error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed top-4 right-4 bg-green-100 border border-green-300 rounded-lg p-4 max-w-sm z-50">
      <div className="space-y-3">
        <div className="flex items-center gap-3">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <div>
            <div className="font-medium text-green-800">Brevo Ativo</div>
            <div className="text-sm text-green-700">Emails reais com IA</div>
          </div>
        </div>

        {status.accountInfo && (
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center p-2 bg-white rounded">
              <Mail className="w-4 h-4 mx-auto mb-1 text-blue-600" />
              <div className="font-medium text-gray-900">
                {status.accountInfo.plan?.creditsRemaining || '∞'}
              </div>
              <div className="text-gray-600">Emails hoje</div>
            </div>
            
            <div className="text-center p-2 bg-white rounded">
              <Zap className="w-4 h-4 mx-auto mb-1 text-orange-600" />
              <div className="font-medium text-gray-900">
                {status.accountInfo.plan?.type || 'Free'}
              </div>
              <div className="text-gray-600">Plano</div>
            </div>
            
            <div className="text-center p-2 bg-white rounded">
              <Users className="w-4 h-4 mx-auto mb-1 text-purple-600" />
              <div className="font-medium text-gray-900">∞</div>
              <div className="text-gray-600">Contatos</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BrevoStatus;
