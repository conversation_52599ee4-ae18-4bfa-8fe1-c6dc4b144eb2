import React, { useState } from 'react';

interface FormData {
  name: string;
  email: string;
  phone: string;
}

export const FormularioSimples: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    console.log('🚀 FORMULÁRIO SIMPLES: Enviando email que FUNCIONA...');

    try {
      // BACKEND ROBUSTO - SOLUÇÃO DEFINITIVA
      console.log('🚀 Enviando para backend Node.js...');

      const response = await fetch('http://localhost:3001/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          source: 'frontend-react'
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        console.log('✅ Email ENVIADO com sucesso via Backend!', result);

        // Salvar lead localmente
        const leads = JSON.parse(localStorage.getItem('affiliate_leads') || '[]');
        leads.push({
          ...formData,
          timestamp: new Date().toISOString(),
          method: 'Backend Node.js',
          id: result.data?.leadId || `backend_${Date.now()}`
        });
        localStorage.setItem('affiliate_leads', JSON.stringify(leads));

        setIsSuccess(true);
      } else {
        throw new Error(result.message || 'Backend falhou');
      }
      
    } catch (error) {
      console.error('❌ Erro:', error);
      setError('Erro ao enviar. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  if (isSuccess) {
    return (
      <div className="max-w-md mx-auto bg-white rounded-3xl shadow-2xl p-8 text-center">
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-2xl">🎉</span>
        </div>
        
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Parabéns! Email Enviado!</h2>
        
        <p className="text-gray-600 mb-6">
          Seu email foi enviado com sucesso! Verifique sua caixa de entrada em instantes.
        </p>

        <div className="space-y-3 text-sm text-gray-500 mb-6">
          <div className="flex items-center gap-3 justify-center">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span>Email enviado via FormSubmit</span>
          </div>
          <div className="flex items-center gap-3 justify-center">
            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span>Resposta automática configurada</span>
          </div>
          <div className="flex items-center gap-3 justify-center">
            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
            <span>Links de afiliado incluídos</span>
          </div>
        </div>

        <div className="space-y-3">
          <a 
            href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368" 
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300"
          >
            🔗 ACESSAR GRIP AGORA
          </a>
          
          <div className="grid grid-cols-2 gap-3">
            <a 
              href="https://play.google.com/store/apps/details?id=com.gaiodataos.grip" 
              target="_blank"
              rel="noopener noreferrer"
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg text-sm transition-all duration-300"
            >
              📱 Android
            </a>
            <a 
              href="https://apps.apple.com/us/app/grip-gaiodataos/id6743857628" 
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg text-sm transition-all duration-300"
            >
              📱 iOS
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-3xl shadow-2xl p-8">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-2xl">🚀</span>
        </div>
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Formulário que FUNCIONA</h1>
        <p className="text-gray-600 text-sm">Sistema React com envio de email garantido</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Nome Completo</label>
          <input 
            type="text" 
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            required
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Seu nome completo"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input 
            type="email" 
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            required
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">WhatsApp (opcional)</label>
          <input 
            type="tel" 
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="(11) 99999-9999"
          />
        </div>

        <button 
          type="submit" 
          disabled={isLoading}
          className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none"
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Enviando...
            </span>
          ) : (
            '🚀 QUERO MEU LINK DE AFILIADO'
          )}
        </button>
      </form>

      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 rounded-xl text-red-700 text-sm">
          {error}
        </div>
      )}
    </div>
  );
};
