// ===================================================================
// LAYOUT SYSTEM - AffiliateFlow Pro Design System
// Enterprise-grade responsive layout components
// ===================================================================

import React from 'react';
import { createLogger } from '../../core/utils/logger';
import { theme, BREAKPOINTS } from '../tokens';

const layoutLogger = createLogger('LAYOUT_SYSTEM');

// Container Component - Responsive container with max-widths
export interface ContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: keyof typeof theme.spacing;
  className?: string;
}

export const Container: React.FC<ContainerProps> = ({
  children,
  size = 'xl',
  padding = '4',
  className = '',
}) => {
  const sizeClasses = {
    sm: 'max-w-sm',      // 384px
    md: 'max-w-md',      // 448px
    lg: 'max-w-lg',      // 512px
    xl: 'max-w-4xl',     // 896px
    '2xl': 'max-w-6xl',  // 1152px
    full: 'max-w-full',  // 100%
  };

  const classes = [
    'mx-auto',
    sizeClasses[size],
    `px-${padding}`,
    className,
  ].join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Grid System - Flexible CSS Grid
export interface GridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  gap?: keyof typeof theme.spacing;
  responsive?: {
    sm?: 1 | 2 | 3 | 4 | 5 | 6;
    md?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
    lg?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  };
  className?: string;
}

export const Grid: React.FC<GridProps> = ({
  children,
  cols = 1,
  gap = '4',
  responsive,
  className = '',
}) => {
  const colClasses = `grid-cols-${cols}`;
  const gapClass = `gap-${gap}`;
  
  let responsiveClasses = '';
  if (responsive) {
    if (responsive.sm) responsiveClasses += ` sm:grid-cols-${responsive.sm}`;
    if (responsive.md) responsiveClasses += ` md:grid-cols-${responsive.md}`;
    if (responsive.lg) responsiveClasses += ` lg:grid-cols-${responsive.lg}`;
  }

  const classes = [
    'grid',
    colClasses,
    gapClass,
    responsiveClasses,
    className,
  ].join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Flex Components - Modern flexbox utilities
export interface FlexProps {
  children: React.ReactNode;
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';
  gap?: keyof typeof theme.spacing;
  className?: string;
}

export const Flex: React.FC<FlexProps> = ({
  children,
  direction = 'row',
  align = 'start',
  justify = 'start',
  wrap = 'nowrap',
  gap = '0',
  className = '',
}) => {
  const directionClass = `flex-${direction}`;
  const alignClass = `items-${align}`;
  const justifyClass = `justify-${justify}`;
  const wrapClass = `flex-${wrap}`;
  const gapClass = gap !== '0' ? `gap-${gap}` : '';

  const classes = [
    'flex',
    directionClass,
    alignClass,
    justifyClass,
    wrapClass,
    gapClass,
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Stack Component - Vertical spacing utility
export interface StackProps {
  children: React.ReactNode;
  spacing?: keyof typeof theme.spacing;
  align?: 'start' | 'center' | 'end' | 'stretch';
  className?: string;
}

export const Stack: React.FC<StackProps> = ({
  children,
  spacing = '4',
  align = 'stretch',
  className = '',
}) => {
  const alignClass = `items-${align}`;
  const spaceClass = `space-y-${spacing}`;

  const classes = [
    'flex flex-col',
    alignClass,
    spaceClass,
    className,
  ].join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Section Component - Semantic page sections
export interface SectionProps {
  children: React.ReactNode;
  as?: 'section' | 'div' | 'main' | 'article' | 'aside';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  background?: 'transparent' | 'white' | 'gray' | 'dark';
  className?: string;
}

export const Section: React.FC<SectionProps> = ({
  children,
  as: Component = 'section',
  padding = 'md',
  background = 'transparent',
  className = '',
}) => {
  const paddingClasses = {
    none: '',
    sm: 'py-8',
    md: 'py-16',
    lg: 'py-24',
    xl: 'py-32',
  };

  const backgroundClasses = {
    transparent: '',
    white: 'bg-white',
    gray: 'bg-gray-50',
    dark: 'bg-gray-900',
  };

  const classes = [
    paddingClasses[padding],
    backgroundClasses[background],
    className,
  ].filter(Boolean).join(' ');

  return (
    <Component className={classes}>
      {children}
    </Component>
  );
};

// Card Component - Content containers
export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'outlined' | 'elevated' | 'glass';
  padding?: keyof typeof theme.spacing;
  radius?: keyof typeof theme.borderRadius;
  className?: string;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = '6',
  radius = 'lg',
  className = '',
}) => {
  const variantClasses = {
    default: 'bg-white border border-gray-200',
    outlined: 'bg-transparent border-2 border-gray-300',
    elevated: 'bg-white shadow-lg border-0',
    glass: 'bg-white/80 backdrop-blur-sm border border-white/20',
  };

  const classes = [
    variantClasses[variant],
    `p-${padding}`,
    `rounded-${radius}`,
    className,
  ].join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Responsive Show/Hide Components
export interface ResponsiveProps {
  children: React.ReactNode;
  breakpoint: keyof typeof BREAKPOINTS;
  direction?: 'up' | 'down';
}

export const ShowAt: React.FC<ResponsiveProps> = ({
  children,
  breakpoint,
  direction = 'up',
}) => {
  const classes = direction === 'up' 
    ? `hidden ${breakpoint}:block`
    : `block ${breakpoint}:hidden`;

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

export const HideAt: React.FC<ResponsiveProps> = ({
  children,
  breakpoint,
  direction = 'up',
}) => {
  const classes = direction === 'up' 
    ? `block ${breakpoint}:hidden`
    : `hidden ${breakpoint}:block`;

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Aspect Ratio Component
export interface AspectRatioProps {
  children: React.ReactNode;
  ratio: '1:1' | '4:3' | '16:9' | '21:9' | '3:2';
  className?: string;
}

export const AspectRatio: React.FC<AspectRatioProps> = ({
  children,
  ratio,
  className = '',
}) => {
  const ratioClasses = {
    '1:1': 'aspect-square',
    '4:3': 'aspect-[4/3]',
    '16:9': 'aspect-video',
    '21:9': 'aspect-[21/9]',
    '3:2': 'aspect-[3/2]',
  };

  const classes = [
    'relative',
    ratioClasses[ratio],
    className,
  ].join(' ');

  return (
    <div className={classes}>
      <div className="absolute inset-0">
        {children}
      </div>
    </div>
  );
};

// Center Component - Perfect centering utility
export interface CenterProps {
  children: React.ReactNode;
  axis?: 'both' | 'horizontal' | 'vertical';
  className?: string;
}

export const Center: React.FC<CenterProps> = ({
  children,
  axis = 'both',
  className = '',
}) => {
  const axisClasses = {
    both: 'flex items-center justify-center',
    horizontal: 'flex justify-center',
    vertical: 'flex items-center',
  };

  const classes = [
    axisClasses[axis],
    className,
  ].join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Spacer Component - Flexible spacing
export interface SpacerProps {
  size?: keyof typeof theme.spacing;
  axis?: 'horizontal' | 'vertical';
}

export const Spacer: React.FC<SpacerProps> = ({
  size = '4',
  axis = 'vertical',
}) => {
  const classes = axis === 'vertical' 
    ? `h-${size}` 
    : `w-${size}`;

  return <div className={classes} />;
};

// Layout Hook for responsive utilities
export const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = React.useState<keyof typeof BREAKPOINTS>('xs');

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width >= parseInt(BREAKPOINTS['2xl'])) setBreakpoint('2xl');
      else if (width >= parseInt(BREAKPOINTS.xl)) setBreakpoint('xl');
      else if (width >= parseInt(BREAKPOINTS.lg)) setBreakpoint('lg');
      else if (width >= parseInt(BREAKPOINTS.md)) setBreakpoint('md');
      else if (width >= parseInt(BREAKPOINTS.sm)) setBreakpoint('sm');
      else setBreakpoint('xs');
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

layoutLogger.info('Layout system components initialized');

// Export all layout components
export {
  Container,
  Grid,
  Flex,
  Stack,
  Section,
  Card,
  ShowAt,
  HideAt,
  AspectRatio,
  Center,
  Spacer,
  useBreakpoint,
};
