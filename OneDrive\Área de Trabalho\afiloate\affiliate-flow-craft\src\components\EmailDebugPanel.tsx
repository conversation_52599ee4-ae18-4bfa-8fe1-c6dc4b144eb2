// ===================================================================
// EMAIL DEBUG PANEL - AffiliateFlow Pro
// Debug panel to show email status and demo emails
// ===================================================================

import React, { useState, useEffect } from 'react';
import { Mail, CheckCircle, AlertCircle, RefreshCw, Eye, X } from 'lucide-react';
import { emailFallback } from '../services/emailFallback';
import { config } from '../core/config';

interface EmailDebugPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

const EmailDebugPanel: React.FC<EmailDebugPanelProps> = ({ isVisible, onClose }) => {
  const [emails, setEmails] = useState<any[]>([]);
  const [selectedEmail, setSelectedEmail] = useState<any>(null);

  useEffect(() => {
    if (isVisible) {
      loadEmails();
    }
  }, [isVisible]);

  const loadEmails = () => {
    const demoEmails = emailFallback.getDemoEmails();
    setEmails(demoEmails);
  };

  const clearEmails = () => {
    emailFallback.clearDemoEmails();
    setEmails([]);
    setSelectedEmail(null);
  };

  const getEmailStatus = () => {
    const hasBrevoConfig = !!config.brevo.apiKey;
    const hasEmails = emails.length > 0;
    
    return {
      brevoConfigured: hasBrevoConfig,
      emailsSent: hasEmails,
      totalEmails: emails.length,
      lastEmailTime: emails.length > 0 ? emails[emails.length - 1].timestamp : null
    };
  };

  const status = getEmailStatus();

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Mail className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Email Debug Panel</h2>
              <p className="text-sm text-gray-600">Status do sistema de email e logs</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Left Panel - Status & Email List */}
          <div className="w-1/2 border-r border-gray-200 flex flex-col">
            {/* Status Section */}
            <div className="p-6 border-b border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-4">Status do Sistema</h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  {status.brevoConfigured ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-orange-500" />
                  )}
                  <span className="text-sm">
                    Brevo API: {status.brevoConfigured ? 'Configurado' : 'Não configurado (usando fallback)'}
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  {status.emailsSent ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-gray-400" />
                  )}
                  <span className="text-sm">
                    Emails enviados: {status.totalEmails}
                  </span>
                </div>

                {status.lastEmailTime && (
                  <div className="text-xs text-gray-500">
                    Último email: {new Date(status.lastEmailTime).toLocaleString()}
                  </div>
                )}
              </div>

              <div className="flex gap-2 mt-4">
                <button
                  onClick={loadEmails}
                  className="flex items-center gap-2 px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg text-sm hover:bg-blue-200 transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  Atualizar
                </button>
                <button
                  onClick={clearEmails}
                  className="flex items-center gap-2 px-3 py-1.5 bg-red-100 text-red-700 rounded-lg text-sm hover:bg-red-200 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Limpar
                </button>
              </div>
            </div>

            {/* Email List */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Emails Enviados ({emails.length})</h3>
                
                {emails.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Mail className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>Nenhum email enviado ainda</p>
                    <p className="text-sm">Teste o formulário para ver os emails aqui</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {emails.map((email) => (
                      <div
                        key={email.id}
                        onClick={() => setSelectedEmail(email)}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedEmail?.id === email.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-sm text-gray-900">
                            {email.subject}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${
                            email.method === 'brevo' 
                              ? 'bg-green-100 text-green-700'
                              : 'bg-orange-100 text-orange-700'
                          }`}>
                            {email.method === 'brevo' ? 'Brevo' : 'Fallback'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600">
                          Para: {email.to}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(email.timestamp).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Panel - Email Content */}
          <div className="w-1/2 flex flex-col">
            {selectedEmail ? (
              <>
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Eye className="w-5 h-5 text-gray-600" />
                    <h3 className="font-semibold text-gray-900">Conteúdo do Email</h3>
                  </div>
                  <div className="text-sm text-gray-600">
                    ID: {selectedEmail.id}
                  </div>
                </div>
                
                <div className="flex-1 overflow-y-auto p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Assunto:
                      </label>
                      <div className="p-3 bg-gray-50 rounded-lg text-sm">
                        {selectedEmail.subject}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Para:
                      </label>
                      <div className="p-3 bg-gray-50 rounded-lg text-sm">
                        {selectedEmail.to}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Conteúdo:
                      </label>
                      <div className="p-4 bg-gray-50 rounded-lg text-sm whitespace-pre-wrap font-mono">
                        {selectedEmail.content}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Método de Envio:
                      </label>
                      <div className={`inline-flex px-3 py-1 rounded-full text-sm ${
                        selectedEmail.method === 'brevo'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {selectedEmail.method === 'brevo' ? 'Brevo API' : 'Sistema Fallback'}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Timestamp:
                      </label>
                      <div className="p-3 bg-gray-50 rounded-lg text-sm">
                        {new Date(selectedEmail.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <Eye className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Selecione um email para ver o conteúdo</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailDebugPanel;
