// ===================================================================
// BREVO EMAIL MARKETING SERVICE - AffiliateFlow Pro
// Enterprise-grade email marketing with AI integration
// ===================================================================

import { config } from '../core/config';
import { logger, createLogger, logPerformance } from '../core/utils/logger';
import { ILead, IEmailTemplate, UserNiche, EmailType, IApiResponse, LeadSource, LeadStatus } from '../core/types';
import { aiPersonalization, IAIContentRequest } from './aiPersonalization';
import { emailFallback } from './emailFallback';

const emailLogger = createLogger('BREVO_SERVICE');

// Brevo API Types
interface IBrevoContact {
  email: string;
  attributes: {
    FIRSTNAME: string;
    LASTNAME?: string;
    PHONE?: string;
    NICHE: string;
    SOURCE: string;
    AFFILIATE_LINK: string;
  };
  listIds: number[];
}

interface IBrevoEmailData {
  to: Array<{ email: string; name: string }>;
  templateId: number;
  params: Record<string, string>;
  tags?: string[];
}

interface IBrevoAIRequest {
  prompt: string;
  niche: UserNiche;
  userProfile: {
    name: string;
    interests: string[];
    previousEngagement: string;
  };
}

// AI-Enhanced Email Templates
export const AI_EMAIL_TEMPLATES = {
  WELCOME: {
    id: 'welcome',
    subject: '🤖 Seu Kit GRIP - Renda Recorrente com IA Empresarial',
    aiPrompt: `Crie um email de boas-vindas personalizado para {{name}} no nicho {{niche}}. 
    Foque em:
    - Potencial de ganhos R$15k-55k/mês
    - GRIP como plataforma de IA empresarial
    - Próximos passos claros
    - Tom profissional mas acessível`,
    variables: ['name', 'niche', 'affiliateLink']
  },
  
  TECH_NICHE: {
    id: 'tech_niche',
    subject: '💻 Kit Tech - Scripts IA para Desenvolvedores e Startups',
    aiPrompt: `Crie conteúdo técnico para {{name}} focado em:
    - Como startups usam IA da GRIP
    - Scripts para LinkedIn/Twitter tech
    - Cases de automação financeira
    - ROI para empresas tech`,
    variables: ['name', 'affiliateLink', 'techCases']
  },

  FINANCE_NICHE: {
    id: 'finance_niche', 
    subject: '💰 Kit Finanças - IA que Revoluciona Investimentos Empresariais',
    aiPrompt: `Crie conteúdo financeiro para {{name}} abordando:
    - IA para análise de investimentos empresariais
    - ROI de 300%+ em 6 meses
    - Scripts para influencers de finanças
    - Cases de holdings e gestoras`,
    variables: ['name', 'affiliateLink', 'financeCases']
  },

  BUSINESS_NICHE: {
    id: 'business_niche',
    subject: '🚀 Kit Business - IA que Escala Empresas Exponencialmente', 
    aiPrompt: `Crie conteúdo business para {{name}} destacando:
    - Como IA escala empresas de 7 para 8 dígitos
    - Decisões automatizadas para CEOs
    - Scripts para influencers business
    - Cases de crescimento exponencial`,
    variables: ['name', 'affiliateLink', 'businessCases']
  }
};

class BrevoEmailService {
  private static instance: BrevoEmailService;
  private apiKey: string;
  private baseUrl = 'https://api.brevo.com/v3';
  private isInitialized = false;

  private constructor() {
    // Try to get API key from multiple sources
    this.apiKey = this.getApiKey();
    this.initialize();
  }

  private getApiKey(): string {
    // Try environment variable first
    const envKey = import.meta.env.VITE_BREVO_API_KEY;
    if (envKey && envKey !== '') {
      return envKey;
    }

    // Try hardcoded key as fallback (for immediate testing)
    const hardcodedKey = '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';
    if (hardcodedKey) {
      emailLogger.info('Using hardcoded API key for testing');
      return hardcodedKey;
    }

    emailLogger.warn('No Brevo API key found');
    return '';
  }

  public static getInstance(): BrevoEmailService {
    if (!BrevoEmailService.instance) {
      BrevoEmailService.instance = new BrevoEmailService();
    }
    return BrevoEmailService.instance;
  }

  private async initialize(): Promise<void> {
    try {
      if (!this.apiKey) {
        emailLogger.warn('Brevo API key not configured, running in demo mode');
        return;
      }

      emailLogger.info('Initializing Brevo service', {
        hasApiKey: !!this.apiKey,
        apiKeyLength: this.apiKey.length
      });

      // Test API connection
      const response = await this.makeRequest('GET', '/account');
      if (response.success) {
        this.isInitialized = true;
        emailLogger.info('Brevo service initialized successfully', {
          accountEmail: response.data?.email,
          plan: response.data?.plan?.type
        });
      } else {
        emailLogger.error('Brevo API test failed', {
          error: response.error,
          apiKeyPrefix: this.apiKey.substring(0, 20) + '...'
        });
      }
    } catch (error) {
      emailLogger.error('Failed to initialize Brevo service', {
        error: error instanceof Error ? error.message : 'Unknown error',
        apiKeyPrefix: this.apiKey ? this.apiKey.substring(0, 20) + '...' : 'none'
      });
    }
  }

  private async makeRequest(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<IApiResponse> {
    const requestId = this.generateRequestId();

    emailLogger.debug('Making Brevo API request', {
      method,
      endpoint,
      requestId,
      hasApiKey: !!this.apiKey,
      apiKeyPrefix: this.apiKey ? this.apiKey.substring(0, 20) + '...' : 'none'
    });

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': this.apiKey
        },
        body: data ? JSON.stringify(data) : undefined
      });

      const responseData = await response.json();

      const result = {
        success: response.ok,
        data: responseData,
        error: response.ok ? undefined : {
          code: response.status.toString(),
          message: responseData.message || 'API request failed',
          details: responseData
        },
        timestamp: new Date(),
        requestId
      };

      if (response.ok) {
        emailLogger.debug('Brevo API request successful', {
          method,
          endpoint,
          requestId,
          status: response.status
        });
      } else {
        emailLogger.error('Brevo API request failed', {
          method,
          endpoint,
          requestId,
          status: response.status,
          error: responseData
        });
      }

      return result;
    } catch (error) {
      emailLogger.error('Brevo API network error', {
        method,
        endpoint,
        requestId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: error instanceof Error ? error.message : 'Network request failed'
        },
        timestamp: new Date(),
        requestId
      };
    }
  }

  private generateRequestId(): string {
    return `brevo_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
  }

  @logPerformance('brevo.createContact')
  public async createContact(lead: ILead): Promise<IApiResponse> {
    if (!this.isInitialized) {
      return this.mockResponse('Contact created in demo mode');
    }

    const contactData: IBrevoContact = {
      email: lead.email,
      attributes: {
        FIRSTNAME: lead.name.split(' ')[0],
        LASTNAME: lead.name.split(' ').slice(1).join(' ') || '',
        PHONE: lead.phone || '',
        NICHE: lead.niche.toUpperCase(),
        SOURCE: lead.source,
        AFFILIATE_LINK: lead.affiliateLink
      },
      listIds: [parseInt(config.brevo.listId)]
    };

    const response = await this.makeRequest('POST', '/contacts', contactData);
    
    if (response.success) {
      emailLogger.info('Contact created successfully', { email: lead.email, niche: lead.niche });
    } else {
      emailLogger.error('Failed to create contact', { email: lead.email, error: response.error });
    }

    return response;
  }

  @logPerformance('brevo.generateAIContent')
  private async generateAIContent(contentType: 'subject_line' | 'email_body', lead: ILead): Promise<string> {
    try {
      // Create personalization profile
      const profile = aiPersonalization.createProfile(lead);

      // Prepare AI content request
      const aiRequest: IAIContentRequest = {
        profile,
        contentType,
        context: {
          campaignGoal: 'conversion',
          urgency: 'medium',
          personalData: {
            name: lead.name,
            affiliateLink: lead.affiliateLink,
            niche: lead.niche,
          },
        },
      };

      // Generate AI-powered content
      const aiResponse = await aiPersonalization.generatePersonalizedContent(aiRequest);

      emailLogger.info('AI content generated successfully', {
        userId: lead.id,
        contentType,
        confidence: aiResponse.confidence,
        niche: lead.niche,
      });

      return aiResponse.content;
    } catch (error) {
      emailLogger.warn('AI content generation failed, falling back to template', {
        error: error instanceof Error ? error.message : 'Unknown error',
        leadId: lead.id,
        contentType,
      });

      // Fallback to template-based content
      return this.getBaseContentForNiche(lead.niche, lead);
    }
  }

  private getBaseContentForNiche(niche: UserNiche, lead: ILead): string {
    const templates = {
      tech: `Olá ${lead.name},

🚀 MATERIAIS TECH EXCLUSIVOS - GRIP IA EMPRESARIAL

Como influencer tech, você tem acesso privilegiado ao mercado de IA empresarial mais lucrativo do Brasil!

💻 SCRIPTS PARA SEU PÚBLICO TECH:

📝 POSTS LINKEDIN/TWITTER:
"Acabei de descobrir como startups estão automatizando 80% das operações financeiras com IA. 
A GRIP está revolucionando o mercado B2B. 
Thread sobre como a IA está mudando o game empresarial 👇"

🎯 CASES TECH ESPECÍFICOS:
• Startup reduziu 90% do tempo de análise financeira
• Scale-up automatizou forecasting com 95% de precisão
• Empresa tech economizou R$ 200k/ano em consultoria

💰 COMISSÕES TECH:
• R$ 15.000/mês por empresa que você indicar
• R$ 55.000 quando contratam plano Enterprise

🔗 SEU LINK: ${lead.affiliateLink}

Domine o futuro! 🚀`,

      finance: `Olá ${lead.name},

💎 MATERIAIS FINANÇAS EXCLUSIVOS - GRIP IA EMPRESARIAL

Como influencer de finanças, você tem a oportunidade de indicar a IA mais avançada do mercado financeiro B2B!

💰 SCRIPTS PARA SEU PÚBLICO FINANCEIRO:

📝 POSTS LINKEDIN/INSTAGRAM:
"Enquanto você ensina pessoas físicas a investir, empresas estão usando IA para multiplicar resultados.
A GRIP está automatizando decisões financeiras de empresas que faturam milhões."

🎯 CASES FINANÇAS ESPECÍFICOS:
• Holding aumentou ROI em 40% usando IA da GRIP
• Empresa de investimentos automatizou análise de risco
• Gestora reduziu tempo de due diligence em 70%

💰 COMISSÕES FINANÇAS:
• R$ 15.000/mês recorrente por empresa
• R$ 55.000 no plano Enterprise

🔗 SEU LINK: ${lead.affiliateLink}

Multiplique seus ganhos! 📈`,

      business: `Olá ${lead.name},

🏆 MATERIAIS BUSINESS EXCLUSIVOS - GRIP IA EMPRESARIAL

Como influencer business, você pode indicar a ferramenta que está escalando empresas no Brasil!

🚀 SCRIPTS PARA SEU PÚBLICO EMPRESARIAL:

📝 POSTS LINKEDIN/INSTAGRAM:
"Você ensina a empreender, mas conhece a IA que está escalando empresas de 7 para 8 dígitos?
A GRIP automatiza decisões financeiras que CEOs levavam semanas para tomar."

🎯 CASES BUSINESS ESPECÍFICOS:
• E-commerce escalou de R$ 1M para R$ 10M com IA
• Empresa de serviços otimizou margem em 45%
• Startup conseguiu Series A usando dados da GRIP

💰 COMISSÕES BUSINESS:
• R$ 15.000/mês por empresa que escalar
• R$ 55.000 quando contratam Enterprise

🔗 SEU LINK: ${lead.affiliateLink}

Escale seus ganhos! 📊`,

      ai: `Olá ${lead.name},

🤖 MATERIAIS IA EXCLUSIVOS - GRIP IA EMPRESARIAL

Como especialista em IA, você pode indicar a plataforma mais avançada do mercado B2B!

⚡ SCRIPTS PARA SEU PÚBLICO IA:

📝 POSTS TÉCNICOS:
"A GRIP não é só mais uma ferramenta de IA. É uma plataforma completa que integra ML, NLP e análise preditiva para decisões financeiras empresariais em tempo real."

🎯 CASES IA ESPECÍFICOS:
• Algoritmos proprietários com 95% de precisão
• Processamento de big data financeiro em tempo real
• Integração com ERPs via APIs REST

💰 COMISSÕES IA:
• R$ 15.000/mês por implementação
• R$ 55.000 para projetos Enterprise

🔗 SEU LINK: ${lead.affiliateLink}

Lidere a revolução! 🚀`,

      general: `Olá ${lead.name},

🎉 BEM-VINDO AO PROGRAMA DE AFILIADOS GRIP!

Você agora faz parte do seleto grupo que ganha renda recorrente indicando IA para empresas!

💰 SEU POTENCIAL:
• R$ 15.000/mês por empresa indicada
• R$ 55.000 no plano Enterprise
• Comissões recorrentes

🔗 SEU LINK: ${lead.affiliateLink}

Sucesso! 💪`
    };

    return templates[niche] || templates.general;
  }

  @logPerformance('brevo.sendWelcomeEmail')
  public async sendWelcomeEmail(lead: ILead): Promise<IApiResponse> {
    // Check if Brevo is properly configured
    if (!this.isInitialized || !this.apiKey) {
      emailLogger.warn('Brevo not configured, using fallback service', {
        leadId: lead.id,
        hasApiKey: !!this.apiKey,
        isInitialized: this.isInitialized
      });

      return emailFallback.sendWelcomeEmail(lead);
    }

    try {
      // Generate AI-powered subject line
      const aiSubject = await this.generateAIContent('subject_line', lead);

      // Generate AI-powered email content
      const aiContent = await this.generateAIContent('email_body', lead);

      return this.sendTemplateEmail(lead, 'welcome', {
        name: lead.name,
        subject: aiSubject,
        content: aiContent,
        affiliateLink: lead.affiliateLink
      });
    } catch (error) {
      emailLogger.error('Brevo email failed, using fallback', {
        leadId: lead.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Fallback to simple email service
      return emailFallback.sendWelcomeEmail(lead);
    }
  }

  @logPerformance('brevo.sendNicheEmail')
  public async sendNicheEmail(lead: ILead): Promise<IApiResponse> {
    const templateKey = `${lead.niche.toUpperCase()}_NICHE` as keyof typeof AI_EMAIL_TEMPLATES;
    const template = AI_EMAIL_TEMPLATES[templateKey] || AI_EMAIL_TEMPLATES.WELCOME;
    
    const content = await this.generateAIContent(template, lead);

    return this.sendTemplateEmail(lead, lead.niche, {
      name: lead.name,
      content: content,
      affiliateLink: lead.affiliateLink
    });
  }

  private async sendTemplateEmail(
    lead: ILead,
    templateType: string,
    params: Record<string, string>
  ): Promise<IApiResponse> {
    if (!this.isInitialized) {
      emailLogger.info(`Email sent in demo mode`, { 
        email: lead.email, 
        template: templateType 
      });
      return this.mockResponse('Email sent in demo mode');
    }

    const emailData: IBrevoEmailData = {
      to: [{ email: lead.email, name: lead.name }],
      templateId: parseInt(config.brevo.templateIds[templateType as EmailType] || '1'),
      params,
      tags: [templateType, lead.niche, lead.source]
    };

    const response = await this.makeRequest('POST', '/smtp/email', emailData);
    
    if (response.success) {
      emailLogger.info('Email sent successfully', { 
        email: lead.email, 
        template: templateType,
        niche: lead.niche
      });
    } else {
      emailLogger.error('Failed to send email', { 
        email: lead.email, 
        template: templateType,
        error: response.error 
      });
    }

    return response;
  }

  public async scheduleEmailSequence(lead: ILead): Promise<void> {
    emailLogger.info('Scheduling email sequence', { 
      email: lead.email, 
      niche: lead.niche 
    });

    // Immediate welcome email
    await this.sendWelcomeEmail(lead);

    // Schedule niche-specific email (2 hours later)
    setTimeout(async () => {
      await this.sendNicheEmail(lead);
    }, 2 * 60 * 60 * 1000);

    // Schedule follow-up sequence (24 hours later)
    setTimeout(async () => {
      // Additional follow-up logic here
      emailLogger.info('Follow-up email sequence triggered', { email: lead.email });
    }, 24 * 60 * 60 * 1000);
  }

  private mockResponse(message: string): IApiResponse {
    return {
      success: true,
      data: { message },
      timestamp: new Date(),
      requestId: this.generateRequestId()
    };
  }

  // Send welcome email with PDF attachment
  public async sendWelcomeEmailWithPDF(data: {
    name: string;
    email: string;
    source: string;
  }): Promise<IApiResponse> {
    emailLogger.info('📧 Sending welcome email with PDF attachment', { email: data.email });

    try {
      const firstName = data.name.split(' ')[0];
      const lastName = data.name.split(' ').slice(1).join(' ') || '';

      // Create contact first
      await this.createContact({
        id: `lead_${Date.now()}`,
        name: data.name,
        email: data.email,
        phone: '',
        niche: 'general' as UserNiche,
        source: data.source as LeadSource,
        status: 'captured' as LeadStatus,
        affiliateLink: 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Send email with PDF attachment
      const emailData = {
        sender: {
          email: '<EMAIL>',
          name: 'AffiliateFlow Pro'
        },
        to: [{ email: data.email, name: data.name }],
        subject: '🤖 Seu Kit GRIP - Renda Recorrente com IA Empresarial',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: white; margin-bottom: 10px;">🎉 Parabéns, ${firstName}!</h1>
              <p style="font-size: 18px; opacity: 0.9;">Você agora faz parte do seleto grupo de influencers que geram RENDA RECORRENTE indicando IA para empresas!</p>
            </div>

            <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin-bottom: 25px;">
              <h2 style="color: #FFD700; margin-bottom: 15px;">🤖 SOBRE A GRIP - PLATAFORMA DE IA EMPRESARIAL:</h2>
              <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;">✅ Soluções de IA financeira para empresas</li>
                <li style="margin-bottom: 8px;">✅ Planos de R$ 15.000/mês até R$ 55.000 (Enterprise)</li>
                <li style="margin-bottom: 8px;">✅ Comissões recorrentes para afiliados</li>
                <li style="margin-bottom: 8px;">✅ Suporte técnico especializado</li>
              </ul>
            </div>

            <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin-bottom: 25px;">
              <h2 style="color: #00FF88; margin-bottom: 15px;">💰 SEU POTENCIAL DE GANHOS:</h2>
              <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;">• Base: R$ 15.000/mês recorrente por empresa indicada</li>
                <li style="margin-bottom: 8px;">• Enterprise: Até R$ 55.000 quando empresa contrata plano premium</li>
                <li style="margin-bottom: 8px;">• Escalável: Quanto mais empresas, maior sua renda mensal</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <h2 style="color: #FFD700; margin-bottom: 15px;">🔗 SEU LINK DE AFILIADO:</h2>
              <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368"
                 style="display: inline-block; background: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold; font-size: 16px;">
                🚀 ACESSAR LINK GRIP
              </a>
            </div>

            <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin-bottom: 25px;">
              <h2 style="color: #FFD700; margin-bottom: 15px;">📱 BAIXE O APP GRIP:</h2>
              <div style="text-align: center;">
                <a href="https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share"
                   style="display: inline-block; background: #34A853; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 5px; font-weight: bold;">
                  📱 Android
                </a>
                <a href="https://apps.apple.com/us/app/grip-gaiodataos/id6743857628"
                   style="display: inline-block; background: #007AFF; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 5px; font-weight: bold;">
                  📱 iOS
                </a>
              </div>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.3);">
              <p style="margin-bottom: 10px;">📄 <strong>Material exclusivo em anexo!</strong></p>
              <p style="opacity: 0.8; font-size: 14px;">Confira o PDF "Domine a Nova Economia Digital" que enviamos junto com este email.</p>
            </div>

            <div style="text-align: center; margin-top: 20px;">
              <p style="opacity: 0.8;">Sucesso e bons ganhos! 💪</p>
              <p style="font-weight: bold;">Equipe AffiliateFlow Pro</p>
            </div>
          </div>
        `,
        attachment: [
          {
            name: 'Domine-a-Nova-Economia-Digital.pdf',
            content: await this.getPDFBase64()
          }
        ]
      };

      const response = await this.makeRequest('POST', '/smtp/email', emailData);

      if (response.success) {
        emailLogger.info('✅ Welcome email with PDF sent successfully');
        return { success: true, data: response.data };
      } else {
        throw new Error('Failed to send email');
      }

    } catch (error) {
      emailLogger.error('❌ Failed to send welcome email with PDF', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Get PDF as base64
  private async getPDFBase64(): Promise<string> {
    try {
      emailLogger.info('📄 Loading PDF file...');

      // Try to load PDF from public folder
      const pdfPath = '/pdf/Domine-a-Nova-Economia-Digital.pdf';

      try {
        // In browser environment, we'll fetch the PDF
        const response = await fetch(pdfPath);
        if (response.ok) {
          const arrayBuffer = await response.arrayBuffer();
          const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
          emailLogger.info('✅ PDF loaded successfully');
          return base64;
        } else {
          throw new Error('PDF not found');
        }
      } catch (fetchError) {
        emailLogger.warn('⚠️ Could not load PDF from public folder, sending without attachment');
        return '';
      }

    } catch (error) {
      emailLogger.warn('⚠️ Could not load PDF, sending email without attachment');
      return '';
    }
  }

  // Health check
  public async healthCheck(): Promise<boolean> {
    if (!this.isInitialized) return false;

    const response = await this.makeRequest('GET', '/account');
    return response.success;
  }
}

// Export singleton instance
export const brevoService = BrevoEmailService.getInstance();
