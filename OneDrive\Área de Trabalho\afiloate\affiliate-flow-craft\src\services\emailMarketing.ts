// Advanced Email Marketing Automation System
import emailjs from '@emailjs/browser';

// Email Templates for GRIP Affiliate Marketing
export const EMAIL_TEMPLATES = {
  WELCOME: {
    subject: '🤖 Seu Kit GRIP - Renda Recorrente com IA Empresarial',
    content: `<PERSON><PERSON><PERSON> {{name}},

🎉 PARABÉNS! Você agora faz parte do seleto grupo de influencers que geram RENDA RECORRENTE indicando IA para empresas!

🤖 SOBRE A GRIP - PLATAFORMA DE IA EMPRESARIAL:
✅ Soluções de IA financeira para empresas
✅ Planos de R$ 15.000/mês até R$ 55.000 (Enterprise)
✅ Comissões recorrentes para afiliados
✅ Suporte técnico especializado

💰 SEU POTENCIAL DE GANHOS:
• Base: R$ 15.000/mês recorrente por empresa indicada
• Enterprise: Até R$ 55.000 quando empresa contrata plano premium
• Escalável: Quanto mais empresas, maior sua renda mensal

📱 PRÓXIMOS PASSOS:
1. Acesse seu link: {{affiliateLink}}
2. Baixe o app GRIP para conhecer a plataforma
3. Use os materiais que enviaremos para divulgar

🎯 MATERIAIS EXCLUSIVOS CHEGANDO:
Nas próximas horas você receberá:
• Scripts personalizados para seu nicho
• Templates de posts de alta conversão
• Estratégias de abordagem empresarial
• Cases de sucesso reais

Sucesso e bons ganhos! 💪

Equipe AffiliateFlow Pro`
  },

  TECH_NICHE: {
    subject: '💻 Kit Tech - Scripts IA para Desenvolvedores e Startups',
    content: `Olá {{name}},

🚀 MATERIAIS TECH EXCLUSIVOS - GRIP IA EMPRESARIAL

Como influencer tech, você tem acesso privilegiado ao mercado de IA empresarial mais lucrativo do Brasil!

💻 SCRIPTS PARA SEU PÚBLICO TECH:

📝 POSTS LINKEDIN/TWITTER:
"Acabei de descobrir como startups estão automatizando 80% das operações financeiras com IA.
A GRIP está revolucionando o mercado B2B.
Thread sobre como a IA está mudando o game empresarial 👇"

🎥 STORIES/REELS:
"POV: Você descobre uma IA que faz análise financeira melhor que um CFO
*mostra dashboard da GRIP*
Empresas pagando R$ 15k-55k/mês por isso 🤯"

🎯 CASES TECH ESPECÍFICOS:
• Startup reduziu 90% do tempo de análise financeira
• Scale-up automatizou forecasting com 95% de precisão
• Empresa tech economizou R$ 200k/ano em consultoria

💰 COMISSÕES TECH:
• R$ 15.000/mês por empresa que você indicar
• R$ 55.000 quando contratam plano Enterprise
• Mercado tech tem maior ticket médio

🔗 SEU LINK: {{affiliateLink}}

Domine o futuro! 🚀

Equipe AffiliateFlow Pro`
  },

  FINANCE_NICHE: {
    subject: '💰 Kit Finanças - IA que Revoluciona Investimentos Empresariais',
    content: `Olá {{name}},

💎 MATERIAIS FINANÇAS EXCLUSIVOS - GRIP IA EMPRESARIAL

Como influencer de finanças, você tem a oportunidade de indicar a IA mais avançada do mercado financeiro B2B!

💰 SCRIPTS PARA SEU PÚBLICO FINANCEIRO:

📝 POSTS LINKEDIN/INSTAGRAM:
"Enquanto você ensina pessoas físicas a investir, empresas estão usando IA para multiplicar resultados.
A GRIP está automatizando decisões financeiras de empresas que faturam milhões.
Quer saber como? 👇"

🎥 STORIES/REELS:
"POV: Você descobre que empresas pagam R$ 55k/mês por uma IA financeira
*mostra ROI de 300% em 6 meses*
E você pode ganhar comissão recorrente indicando 💰"

🎯 CASES FINANÇAS ESPECÍFICOS:
• Holding aumentou ROI em 40% usando IA da GRIP
• Empresa de investimentos automatizou análise de risco
• Gestora reduziu tempo de due diligence em 70%

💰 COMISSÕES FINANÇAS:
• R$ 15.000/mês recorrente por empresa
• R$ 55.000 no plano Enterprise
• Mercado financeiro = maior poder aquisitivo

🔗 SEU LINK: {{affiliateLink}}

Multiplique seus ganhos! 📈

Equipe AffiliateFlow Pro`
  },

  BUSINESS_NICHE: {
    subject: '🚀 Kit Business - IA que Escala Empresas Exponencialmente',
    content: `Olá {{name}},

🏆 MATERIAIS BUSINESS EXCLUSIVOS - GRIP IA EMPRESARIAL

Como influencer business, você pode indicar a ferramenta que está escalando empresas no Brasil!

🚀 SCRIPTS PARA SEU PÚBLICO EMPRESARIAL:

📝 POSTS LINKEDIN/INSTAGRAM:
"Você ensina a empreender, mas conhece a IA que está escalando empresas de 7 para 8 dígitos?
A GRIP automatiza decisões financeiras que CEOs levavam semanas para tomar.
Case real: empresa cresceu 300% em 1 ano 👇"

🎥 STORIES/REELS:
"POV: Você descobre a IA que CEOs usam para tomar decisões de milhões
*mostra dashboard executivo*
E empresas pagam R$ 55k/mês por isso 🤯"

🎯 CASES BUSINESS ESPECÍFICOS:
• E-commerce escalou de R$ 1M para R$ 10M com IA
• Empresa de serviços otimizou margem em 45%
• Startup conseguiu Series A usando dados da GRIP

💰 COMISSÕES BUSINESS:
• R$ 15.000/mês por empresa que escalar
• R$ 55.000 quando contratam Enterprise
• Empresários investem mais em crescimento

🔗 SEU LINK: {{affiliateLink}}

Escale seus ganhos! 📊

Equipe AffiliateFlow Pro`
  }
};

export interface EmailSequenceData {
  name: string;
  email: string;
  leadSource: string;
  affiliateLink?: string;
  customData?: Record<string, any>;
}

// Email Marketing Service Class
export class EmailMarketingService {
  private serviceId: string;
  private publicKey: string;

  constructor() {
    this.serviceId = import.meta.env.VITE_EMAILJS_SERVICE_ID || '';
    this.publicKey = import.meta.env.VITE_EMAILJS_PUBLIC_KEY || '';
    
    if (this.publicKey) {
      emailjs.init(this.publicKey);
    }
  }

  // Send welcome email with immediate value
  async sendWelcomeEmail(data: EmailSequenceData): Promise<boolean> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.WELCOME.subject,
        
        // Personalized content
        lead_source: data.leadSource,
        affiliate_link: data.affiliateLink || '',
        
        // Welcome message with immediate value
        welcome_message: this.getWelcomeMessage(data.name),
        
        // Bonus content
        bonus_content: this.getBonusContent(),
        
        // Next steps
        next_steps: this.getNextSteps(),
        
        // Social proof
        social_proof: this.getSocialProof()
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.WELCOME.template, templateParams);
      
      // Schedule follow-up emails
      this.scheduleEmailSequence(data);
      
      return true;
    } catch (error) {
      console.error('Welcome email error:', error);
      return false;
    }
  }

  // Schedule automated email sequence
  private scheduleEmailSequence(data: EmailSequenceData): void {
    // Day 1: Onboarding and first strategy
    setTimeout(() => {
      this.sendOnboardingDay1(data);
    }, 24 * 60 * 60 * 1000); // 24 hours

    // Day 3: Advanced strategies
    setTimeout(() => {
      this.sendOnboardingDay3(data);
    }, 3 * 24 * 60 * 60 * 1000); // 3 days

    // Day 7: Final push with urgency
    setTimeout(() => {
      this.sendOnboardingDay7(data);
    }, 7 * 24 * 60 * 60 * 1000); // 7 days
  }

  // Day 1 onboarding email
  private async sendOnboardingDay1(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY1.subject,
        
        main_content: `
          Olá ${data.name}!
          
          Espero que esteja animado(a) para começar sua jornada rumo aos R$ 15.000/mês!
          
          Hoje vou compartilhar com você a PRIMEIRA estratégia que mudou a vida de mais de 2.847 afiliados:
          
          🎯 **ESTRATÉGIA #1: O Método da Indicação Inteligente**
          
          Ao invés de "vender" diretamente, você vai EDUCAR seu público sobre:
          ✅ Como a IA está revolucionando empresas
          ✅ Por que grandes corporações estão investindo bilhões
          ✅ Como pequenas empresas podem competir usando as mesmas ferramentas
          
          **AÇÃO PARA HOJE:**
          1. Acesse seu painel de afiliado: ${data.affiliateLink || '[LINK_SERÁ_ENVIADO]'}
          2. Baixe o kit de materiais exclusivos
          3. Compartilhe o primeiro conteúdo educativo
          
          **RESULTADO ESPERADO:** Suas primeiras comissões em 48-72h
          
          Amanhã vou revelar a Estratégia #2 que pode TRIPLICAR seus resultados...
          
          Forte abraço,
          Marcus Silva
          
          P.S.: Mais de 847 pessoas já estão aplicando essa estratégia HOJE. Não fique para trás!
        `,
        
        cta_text: 'ACESSAR PAINEL DE AFILIADO',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY1.template, templateParams);
    } catch (error) {
      console.error('Day 1 email error:', error);
    }
  }

  // Day 3 advanced strategies
  private async sendOnboardingDay3(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY3.subject,
        
        main_content: `
          ${data.name}, como estão os primeiros resultados?
          
          Espero que já tenha visto suas primeiras comissões chegando! 💰
          
          Hoje vou revelar as 3 ESTRATÉGIAS SECRETAS que os top afiliados (R$ 50k/mês) usam:
          
          🔥 **ESTRATÉGIA SECRETA #1: O Funil da Autoridade**
          - Como se posicionar como especialista em IA
          - Scripts prontos para redes sociais
          - Técnica do "Problema → Solução → Prova"
          
          ⚡ **ESTRATÉGIA SECRETA #2: O Método da Escassez Inteligente**
          - Como criar urgência sem ser "vendedor"
          - Gatilhos psicológicos que convertem 3x mais
          - Timing perfeito para máxima conversão
          
          🎯 **ESTRATÉGIA SECRETA #3: A Rede de Indicações Exponencial**
          - Como transformar 1 cliente em 10 indicações
          - Sistema de recompensas que funciona
          - Automação completa do processo
          
          **BÔNUS EXCLUSIVO:** Planilha de controle de comissões + Calculadora de metas
          
          Acesse agora: ${data.affiliateLink || '[LINK_PAINEL]'}
          
          Nos vemos no topo!
          Marcus Silva
        `,
        
        bonus_content: 'Planilha Exclusiva + Scripts Prontos',
        cta_text: 'BAIXAR ESTRATÉGIAS SECRETAS',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY3.template, templateParams);
    } catch (error) {
      console.error('Day 3 email error:', error);
    }
  }

  // Day 7 final push with urgency
  private async sendOnboardingDay7(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY7.subject,
        
        main_content: `
          ${data.name}, esta é sua ÚLTIMA CHANCE...
          
          Nos últimos 7 dias, você recebeu estratégias que valem mais de R$ 5.000.
          
          Mas hoje quero fazer uma oferta ESPECIAL só para você:
          
          🚨 **ACESSO VIP AO MÉTODO DOS R$ 15K/MÊS** 🚨
          
          ✅ Mentoria individual comigo (1h)
          ✅ Grupo VIP no Telegram (apenas 100 vagas)
          ✅ Scripts de conversão que faturam R$ 50k/mês
          ✅ Suporte prioritário 24/7
          ✅ Comissões DOBRADAS nos primeiros 30 dias
          
          **VALOR NORMAL:** R$ 2.997
          **SEU PREÇO HOJE:** R$ 497 (83% OFF)
          
          ⏰ **OFERTA EXPIRA EM 24 HORAS**
          
          Apenas 23 vagas restantes...
          
          Esta é SUA chance de sair do comum e entrar para o grupo dos R$ 15k/mês.
          
          Não deixe para amanhã o que pode mudar sua vida HOJE!
          
          [GARANTIR MINHA VAGA VIP]
          
          Último aviso,
          Marcus Silva
          
          P.S.: Se não aproveitar hoje, essa oferta não voltará. Decida AGORA!
        `,
        
        urgency_timer: '24:00:00',
        special_price: 'R$ 497',
        normal_price: 'R$ 2.997',
        discount: '83% OFF',
        cta_text: 'GARANTIR VAGA VIP AGORA',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY7.template, templateParams);
    } catch (error) {
      console.error('Day 7 email error:', error);
    }
  }

  // Helper methods for content generation
  private getWelcomeMessage(name: string): string {
    return `
      Olá ${name}!
      
      Seja muito bem-vindo(a) ao AffiliateFlow Premium! 🎉
      
      Você acabou de dar o primeiro passo rumo à sua independência financeira.
      
      Nos próximos minutos, você vai descobrir exatamente como transformar
      indicações simples em uma renda recorrente de R$ 15.000/mês.
    `;
  }

  private getBonusContent(): string {
    return `
      🎁 **BÔNUS EXCLUSIVOS PARA VOCÊ:**
      
      ✅ E-book: "7 Segredos dos Afiliados Milionários"
      ✅ Planilha de Controle de Comissões
      ✅ Scripts Prontos para Redes Sociais
      ✅ Vídeo: "Primeira Venda em 24h"
      ✅ Acesso ao Grupo VIP no Telegram
    `;
  }

  private getNextSteps(): string {
    return `
      📋 **SEUS PRÓXIMOS PASSOS:**
      
      1️⃣ Confirme seu email (se ainda não fez)
      2️⃣ Acesse seu painel de afiliado
      3️⃣ Baixe os materiais exclusivos
      4️⃣ Faça sua primeira indicação
      5️⃣ Acompanhe suas comissões em tempo real
    `;
  }

  private getSocialProof(): string {
    return `
      🏆 **RESULTADOS REAIS DOS NOSSOS AFILIADOS:**

      "Em 30 dias faturei R$ 12.847 só com indicações!" - Ana Paula, SP
      "Método simples que realmente funciona. R$ 8.500 no primeiro mês!" - Carlos, RJ
      "Nunca pensei que fosse tão fácil. R$ 15.200 em 45 dias!" - Mariana, MG
    `;
  }

  // Get niche-specific social proof
  private getNicheSpecificProof(niche: UserNiche): string {
    const proofByNiche = {
      [UserNiche.TECH]: `
        🏆 **RESULTADOS DE TECH INFLUENCERS:**

        "@RafaelTech: R$ 31.500 em 60 dias falando sobre IA financeira"
        "Dev que automatizou vendas: R$ 45.000/mês com GRIP"
        "Startup que implementou IA: contrato de R$ 55K fechado"
      `,
      [UserNiche.FINANCE]: `
        🏆 **RESULTADOS DE FINFLUENCERS:**

        "Investidor que diversificou: R$ 55.800 em 90 dias"
        "@CarlaFinance: R$ 25.000/mês ensinando ROI com IA"
        "Consultor financeiro: 3 contratos Enterprise em 60 dias"
      `,
      [UserNiche.BUSINESS]: `
        🏆 **RESULTADOS DE BUSINESS INFLUENCERS:**

        "@CarlaEmpreende: R$ 55.800 ensinando ROI empresarial"
        "Empreendedor que escalou: R$ 40.000/mês com indicações"
        "Consultor B2B: 5 empresas fecharam plano Enterprise"
      `,
      [UserNiche.AI]: `
        🏆 **RESULTADOS DE ESPECIALISTAS IA:**

        "Expert em IA: R$ 60.000/mês só com indicações"
        "Consultor técnico: 8 contratos Enterprise em 90 dias"
        "Autoridade em IA: renda recorrente de R$ 75K/mês"
      `
    };

    return proofByNiche[niche] || this.getSocialProof();
  }

  // Get niche-specific CTA
  private getNicheCTA(niche: UserNiche): string {
    const ctaByNiche = {
      [UserNiche.TECH]: 'ACESSAR GRIP - IA PARA DEVS',
      [UserNiche.FINANCE]: 'DESCOBRIR IA FINANCEIRA',
      [UserNiche.BUSINESS]: 'ESCALAR COM IA EMPRESARIAL',
      [UserNiche.AI]: 'MONETIZAR EXPERTISE IA'
    };

    return ctaByNiche[niche] || 'COMEÇAR COM GRIP IA';
  }

  // Send personalized onboarding based on niche
  private async sendPersonalizedOnboarding(data: EmailSequenceData, niche: UserNiche): Promise<void> {
    try {
      const nicheScript = this.getPersonalizedContent(niche, 'scripts');
      const nicheCase = this.getPersonalizedContent(niche, 'caseStudies');

      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - GRIP IA Empresarial',
        subject: `💰 ${data.name}, sua primeira estratégia R$ 15K está aqui!`,

        niche: niche,
        personalized_script: nicheScript,
        personalized_case: nicheCase,

        main_content: this.getOnboardingContent(data.name, niche),
        cta_text: this.getNicheCTA(niche),
        cta_link: data.affiliateLink || 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368'
      };

      await emailjs.send(this.serviceId, this.templateId, templateParams);
    } catch (error) {
      console.error('Personalized onboarding error:', error);
    }
  }

  // Get onboarding content based on niche
  private getOnboardingContent(name: string, niche: UserNiche): string {
    const contentByNiche = {
      [UserNiche.TECH]: `
        Olá ${name}!

        Como prometido, aqui está sua PRIMEIRA estratégia para faturar R$ 15.000+ com IA empresarial:

        🎯 **ESTRATÉGIA #1: O Método Tech Authority**

        Sua audiência tech já entende o valor da IA. Use isso a seu favor:

        ✅ Fale sobre IA financeira como revolução tecnológica
        ✅ Mostre cases de startups que usam GRIP
        ✅ Explique a arquitetura por trás da IA empresarial
        ✅ Conecte desenvolvimento com ROI empresarial

        **SCRIPT PRONTO:** "A IA que está mudando o mercado financeiro"
        **RESULTADO:** Cada empresa que contratar paga R$ 55.000

        Próxima estratégia em 48h...
      `,
      [UserNiche.FINANCE]: `
        Olá ${name}!

        Sua PRIMEIRA estratégia para R$ 15.000+ mensais com IA empresarial:

        💰 **ESTRATÉGIA #1: Diversificação Inteligente**

        Sua audiência busca diversificação. IA empresarial é o novo ativo:

        ✅ Ensine IA como nova classe de investimento
        ✅ Mostre ROI de empresas que usam GRIP
        ✅ Compare com outros investimentos
        ✅ Foque na renda recorrente R$ 15K+

        **SCRIPT PRONTO:** "O investimento que empresas pagam R$ 55K"
        **RESULTADO:** Renda passiva real com tecnologia

        Estratégia #2 chegando em breve...
      `
    };

    return contentByNiche[niche] || `
      Olá ${name}!

      Sua primeira estratégia para R$ 15.000+ mensais está aqui!

      🎯 **ESTRATÉGIA #1: Indicação Inteligente**

      ✅ Eduque sobre IA empresarial
      ✅ Mostre cases reais de sucesso
      ✅ Conecte com tomadores de decisão
      ✅ Foque no valor R$ 55K Enterprise

      Próxima estratégia em 48h...
    `;
  }
}

// Initialize email marketing service
export const emailMarketing = new EmailMarketingService();
