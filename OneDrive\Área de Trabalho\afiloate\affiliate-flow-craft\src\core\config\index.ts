// ===================================================================
// CONFIGURATION MANAGER - AffiliateFlow Pro
// Enterprise-grade configuration with validation and type safety
// ===================================================================

import { IConfiguration, IAppConfig, IBrevoConfig, IAnalyticsConfig, IGripConfig } from '../types';

class ConfigurationManager {
  private static instance: ConfigurationManager;
  private config: IConfiguration;

  private constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  private loadConfiguration(): IConfiguration {
    return {
      app: this.loadAppConfig(),
      brevo: this.loadBrevoConfig(),
      analytics: this.loadAnalyticsConfig(),
      grip: this.loadGripConfig()
    };
  }

  private loadAppConfig(): IAppConfig {
    return {
      environment: (import.meta.env.VITE_ENVIRONMENT as 'development' | 'staging' | 'production') || 'development',
      apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
      enableAI: import.meta.env.VITE_ENABLE_AI === 'true',
      enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
      debugMode: import.meta.env.VITE_DEBUG_MODE === 'true'
    };
  }

  private loadBrevoConfig(): IBrevoConfig {
    return {
      apiKey: import.meta.env.VITE_BREVO_API_KEY || '',
      listId: import.meta.env.VITE_BREVO_LIST_ID || '',
      templateIds: {
        welcome: import.meta.env.VITE_BREVO_TEMPLATE_WELCOME || '',
        nurture: import.meta.env.VITE_BREVO_TEMPLATE_NURTURE || '',
        conversion: import.meta.env.VITE_BREVO_TEMPLATE_CONVERSION || '',
        reactivation: import.meta.env.VITE_BREVO_TEMPLATE_REACTIVATION || '',
        upsell: import.meta.env.VITE_BREVO_TEMPLATE_UPSELL || ''
      },
      webhookUrl: import.meta.env.VITE_BREVO_WEBHOOK_URL
    };
  }

  private loadAnalyticsConfig(): IAnalyticsConfig {
    return {
      googleAnalyticsId: import.meta.env.VITE_GA_MEASUREMENT_ID,
      facebookPixelId: import.meta.env.VITE_FACEBOOK_PIXEL_ID,
      hotjarId: import.meta.env.VITE_HOTJAR_ID,
      enableDebug: import.meta.env.VITE_ANALYTICS_DEBUG === 'true'
    };
  }

  private loadGripConfig(): IGripConfig {
    return {
      affiliateBaseUrl: 'https://grip.gaiodataos.com/',
      affiliateId: 'f722bc5f-c550-4368-a50f-d727e7abc368',
      androidAppUrl: 'https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share',
      iosAppUrl: 'https://apps.apple.com/us/app/grip-gaiodataos/id6743857628'
    };
  }

  private validateConfiguration(): void {
    const errors: string[] = [];

    // Validate Brevo configuration
    if (this.config.app.enableAI && !this.config.brevo.apiKey) {
      errors.push('VITE_BREVO_API_KEY is required when AI is enabled');
    }

    // Validate Analytics configuration
    if (this.config.app.enableAnalytics) {
      if (!this.config.analytics.googleAnalyticsId && !this.config.analytics.facebookPixelId) {
        console.warn('⚠️ No analytics providers configured');
      }
    }

    // Validate environment-specific requirements
    if (this.config.app.environment === 'production') {
      if (this.config.app.debugMode) {
        errors.push('Debug mode should be disabled in production');
      }
      if (!this.config.brevo.apiKey) {
        errors.push('Brevo API key is required in production');
      }
    }

    if (errors.length > 0) {
      console.error('❌ Configuration validation failed:', errors);
      if (this.config.app.environment === 'production') {
        throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
      }
    } else {
      console.log('✅ Configuration validated successfully');
    }
  }

  // Public getters
  public get app(): IAppConfig {
    return { ...this.config.app };
  }

  public get brevo(): IBrevoConfig {
    return { ...this.config.brevo };
  }

  public get analytics(): IAnalyticsConfig {
    return { ...this.config.analytics };
  }

  public get grip(): IGripConfig {
    return { ...this.config.grip };
  }

  public get all(): IConfiguration {
    return { ...this.config };
  }

  // Utility methods
  public isProduction(): boolean {
    return this.config.app.environment === 'production';
  }

  public isDevelopment(): boolean {
    return this.config.app.environment === 'development';
  }

  public isAIEnabled(): boolean {
    return this.config.app.enableAI;
  }

  public isAnalyticsEnabled(): boolean {
    return this.config.app.enableAnalytics;
  }

  public isDebugMode(): boolean {
    return this.config.app.debugMode;
  }

  // Dynamic configuration updates (for testing)
  public updateConfig(updates: Partial<IConfiguration>): void {
    if (this.isProduction()) {
      throw new Error('Configuration cannot be updated in production');
    }
    
    this.config = { ...this.config, ...updates };
    this.validateConfiguration();
  }

  // Configuration status
  public getStatus(): {
    isValid: boolean;
    missingKeys: string[];
    warnings: string[];
  } {
    const missingKeys: string[] = [];
    const warnings: string[] = [];

    // Check required keys
    if (!this.config.brevo.apiKey) missingKeys.push('VITE_BREVO_API_KEY');
    if (!this.config.brevo.listId) missingKeys.push('VITE_BREVO_LIST_ID');

    // Check optional but recommended keys
    if (!this.config.analytics.googleAnalyticsId) {
      warnings.push('Google Analytics not configured');
    }
    if (!this.config.analytics.facebookPixelId) {
      warnings.push('Facebook Pixel not configured');
    }

    return {
      isValid: missingKeys.length === 0,
      missingKeys,
      warnings
    };
  }
}

// Export singleton instance
export const config = ConfigurationManager.getInstance();

// Export types for external use
export type { IConfiguration, IAppConfig, IBrevoConfig, IAnalyticsConfig, IGripConfig };

// Environment-specific configurations
export const ENV_CONFIGS = {
  development: {
    apiBaseUrl: 'http://localhost:3000/api',
    enableAI: true,
    enableAnalytics: false,
    debugMode: true
  },
  staging: {
    apiBaseUrl: 'https://staging-api.affiliateflowpro.com/api',
    enableAI: true,
    enableAnalytics: true,
    debugMode: true
  },
  production: {
    apiBaseUrl: 'https://api.affiliateflowpro.com/api',
    enableAI: true,
    enableAnalytics: true,
    debugMode: false
  }
} as const;

// Configuration validation utilities
export const validateEnvironmentVariables = (): void => {
  const status = config.getStatus();
  
  if (!status.isValid) {
    console.error('❌ Missing required environment variables:', status.missingKeys);
    console.log('📝 Create a .env file with the following variables:');
    status.missingKeys.forEach(key => {
      console.log(`${key}=your_value_here`);
    });
  }

  if (status.warnings.length > 0) {
    console.warn('⚠️ Configuration warnings:', status.warnings);
  }

  if (status.isValid && status.warnings.length === 0) {
    console.log('✅ All configuration variables are properly set');
  }
};
