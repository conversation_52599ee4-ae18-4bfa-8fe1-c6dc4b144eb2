// ===================================================================
// DESIGN SYSTEM TOKENS - AffiliateFlow Pro
// Enterprise-grade design tokens with semantic naming and theming
// ===================================================================

import { createLogger } from '../core/utils/logger';

const designLogger = createLogger('DESIGN_SYSTEM');

// Base Color Palette - Scientifically chosen for conversion optimization
export const BASE_COLORS = {
  // Primary Brand Colors - High-converting blues and oranges
  primary: {
    50: '#eff6ff',   // Ultra light blue
    100: '#dbeafe',  // Very light blue
    200: '#bfdbfe',  // Light blue
    300: '#93c5fd',  // Medium light blue
    400: '#60a5fa',  // Medium blue
    500: '#3b82f6',  // Base blue (primary)
    600: '#2563eb',  // Dark blue
    700: '#1d4ed8',  // Darker blue
    800: '#1e40af',  // Very dark blue
    900: '#1e3a8a',  // Ultra dark blue
  },

  // Secondary Colors - High-energy orange for CTAs
  secondary: {
    50: '#fff7ed',   // Ultra light orange
    100: '#ffedd5',  // Very light orange
    200: '#fed7aa',  // Light orange
    300: '#fdba74',  // Medium light orange
    400: '#fb923c',  // Medium orange
    500: '#f97316',  // Base orange (secondary)
    600: '#ea580c',  // Dark orange
    700: '#c2410c',  // Darker orange
    800: '#9a3412',  // Very dark orange
    900: '#7c2d12',  // Ultra dark orange
  },

  // Success Colors - Green for positive actions
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',  // Base success
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },

  // Warning Colors - Amber for attention
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',  // Base warning
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },

  // Error Colors - Red for errors and urgency
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',  // Base error
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },

  // Neutral Colors - Sophisticated grays
  neutral: {
    0: '#ffffff',    // Pure white
    50: '#f9fafb',   // Almost white
    100: '#f3f4f6',  // Very light gray
    200: '#e5e7eb',  // Light gray
    300: '#d1d5db',  // Medium light gray
    400: '#9ca3af',  // Medium gray
    500: '#6b7280',  // Base gray
    600: '#4b5563',  // Dark gray
    700: '#374151',  // Darker gray
    800: '#1f2937',  // Very dark gray
    900: '#111827',  // Ultra dark gray
    950: '#030712',  // Almost black
  },
} as const;

// Semantic Color System - Business logic colors
export const SEMANTIC_COLORS = {
  // Background Colors
  background: {
    primary: BASE_COLORS.neutral[0],      // White
    secondary: BASE_COLORS.neutral[50],   // Almost white
    tertiary: BASE_COLORS.neutral[100],   // Very light gray
    dark: BASE_COLORS.neutral[900],       // Dark background
    darker: BASE_COLORS.neutral[950],     // Darker background
  },

  // Text Colors
  text: {
    primary: BASE_COLORS.neutral[900],    // Dark text
    secondary: BASE_COLORS.neutral[600],  // Medium text
    tertiary: BASE_COLORS.neutral[400],   // Light text
    inverse: BASE_COLORS.neutral[0],      // White text
    muted: BASE_COLORS.neutral[500],      // Muted text
  },

  // Border Colors
  border: {
    light: BASE_COLORS.neutral[200],      // Light border
    medium: BASE_COLORS.neutral[300],     // Medium border
    dark: BASE_COLORS.neutral[400],       // Dark border
    focus: BASE_COLORS.primary[500],      // Focus border
  },

  // Interactive Colors
  interactive: {
    primary: BASE_COLORS.primary[500],    // Primary buttons
    primaryHover: BASE_COLORS.primary[600], // Primary hover
    secondary: BASE_COLORS.secondary[500], // Secondary buttons
    secondaryHover: BASE_COLORS.secondary[600], // Secondary hover
    link: BASE_COLORS.primary[600],       // Links
    linkHover: BASE_COLORS.primary[700],  // Link hover
  },

  // Status Colors
  status: {
    success: BASE_COLORS.success[500],
    warning: BASE_COLORS.warning[500],
    error: BASE_COLORS.error[500],
    info: BASE_COLORS.primary[500],
  },
} as const;

// Typography System - Optimized for readability and conversion
export const TYPOGRAPHY = {
  // Font Families
  fontFamily: {
    sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
    mono: ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'monospace'],
    display: ['Cal Sans', 'Inter', 'system-ui', 'sans-serif'],
  },

  // Font Sizes - Modular scale for hierarchy
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem', // 60px
    '7xl': '4.5rem',  // 72px
    '8xl': '6rem',    // 96px
    '9xl': '8rem',    // 128px
  },

  // Font Weights
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },

  // Line Heights - Optimized for readability
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },

  // Letter Spacing
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
} as const;

// Spacing System - 8px grid system
export const SPACING = {
  0: '0px',
  px: '1px',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px
  9: '2.25rem',     // 36px
  10: '2.5rem',     // 40px
  11: '2.75rem',    // 44px
  12: '3rem',       // 48px
  14: '3.5rem',     // 56px
  16: '4rem',       // 64px
  20: '5rem',       // 80px
  24: '6rem',       // 96px
  28: '7rem',       // 112px
  32: '8rem',       // 128px
  36: '9rem',       // 144px
  40: '10rem',      // 160px
  44: '11rem',      // 176px
  48: '12rem',      // 192px
  52: '13rem',      // 208px
  56: '14rem',      // 224px
  60: '15rem',      // 240px
  64: '16rem',      // 256px
  72: '18rem',      // 288px
  80: '20rem',      // 320px
  96: '24rem',      // 384px
} as const;

// Border Radius System
export const BORDER_RADIUS = {
  none: '0px',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',   // Fully rounded
} as const;

// Shadow System - Depth and elevation
export const SHADOWS = {
  none: 'none',
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  glow: '0 0 20px rgb(59 130 246 / 0.5)',
  glowOrange: '0 0 20px rgb(249 115 22 / 0.5)',
} as const;

// Animation & Transition System
export const ANIMATIONS = {
  // Transition Durations
  duration: {
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },

  // Transition Timing Functions
  timing: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // Common Animations
  keyframes: {
    fadeIn: 'fadeIn 0.3s ease-out',
    slideUp: 'slideUp 0.3s ease-out',
    slideDown: 'slideDown 0.3s ease-out',
    scaleIn: 'scaleIn 0.2s ease-out',
    pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    spin: 'spin 1s linear infinite',
    bounce: 'bounce 1s infinite',
  },
} as const;

// Breakpoints - Mobile-first responsive design
export const BREAKPOINTS = {
  xs: '475px',      // Extra small devices
  sm: '640px',      // Small devices (landscape phones)
  md: '768px',      // Medium devices (tablets)
  lg: '1024px',     // Large devices (laptops)
  xl: '1280px',     // Extra large devices (desktops)
  '2xl': '1536px',  // 2X large devices (large desktops)
} as const;

// Z-Index System - Layering hierarchy
export const Z_INDEX = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const;

// Theme Configuration
export interface Theme {
  colors: typeof SEMANTIC_COLORS;
  typography: typeof TYPOGRAPHY;
  spacing: typeof SPACING;
  borderRadius: typeof BORDER_RADIUS;
  shadows: typeof SHADOWS;
  animations: typeof ANIMATIONS;
  breakpoints: typeof BREAKPOINTS;
  zIndex: typeof Z_INDEX;
}

export const theme: Theme = {
  colors: SEMANTIC_COLORS,
  typography: TYPOGRAPHY,
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  shadows: SHADOWS,
  animations: ANIMATIONS,
  breakpoints: BREAKPOINTS,
  zIndex: Z_INDEX,
};

// Theme Provider Hook
export const useTheme = () => {
  designLogger.debug('Theme accessed', { timestamp: new Date().toISOString() });
  return theme;
};

// CSS Custom Properties Generator
export const generateCSSCustomProperties = (): string => {
  const cssVars: string[] = [];

  // Generate color variables
  Object.entries(SEMANTIC_COLORS).forEach(([category, colors]) => {
    Object.entries(colors).forEach(([name, value]) => {
      cssVars.push(`  --color-${category}-${name}: ${value};`);
    });
  });

  // Generate spacing variables
  Object.entries(SPACING).forEach(([name, value]) => {
    cssVars.push(`  --spacing-${name}: ${value};`);
  });

  // Generate typography variables
  Object.entries(TYPOGRAPHY.fontSize).forEach(([name, value]) => {
    cssVars.push(`  --font-size-${name}: ${value};`);
  });

  return `:root {\n${cssVars.join('\n')}\n}`;
};

designLogger.info('Design system tokens initialized', {
  colorsCount: Object.keys(SEMANTIC_COLORS).length,
  spacingCount: Object.keys(SPACING).length,
  breakpointsCount: Object.keys(BREAKPOINTS).length,
});
