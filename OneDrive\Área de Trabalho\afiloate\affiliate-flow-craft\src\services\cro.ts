// Conversion Rate Optimization (CRO) Service
// A/B Testing, Copy Optimization, and UX Improvements

interface ABTestVariant {
  id: string;
  name: string;
  weight: number; // Percentage of traffic (0-100)
  config: Record<string, any>;
}

interface ABTest {
  id: string;
  name: string;
  description: string;
  variants: ABTestVariant[];
  isActive: boolean;
  startDate: Date;
  endDate?: Date;
}

interface ConversionEvent {
  testId: string;
  variantId: string;
  userId: string;
  eventType: 'view' | 'click' | 'conversion';
  timestamp: Date;
  value?: number;
}

class CROService {
  private tests: Map<string, ABTest> = new Map();
  private userVariants: Map<string, Map<string, string>> = new Map();
  private events: ConversionEvent[] = [];

  constructor() {
    this.initializeTests();
    this.loadUserVariants();
  }

  // Initialize default A/B tests
  private initializeTests(): void {
    // Hero Section Headline Test
    this.addTest({
      id: 'hero_headline',
      name: 'Hero Headline Test',
      description: 'Test different headlines for maximum impact',
      isActive: true,
      startDate: new Date(),
      variants: [
        {
          id: 'control',
          name: 'Control - Original',
          weight: 50,
          config: {
            headline: 'Renda Recorrente Indicando IA para Empresas',
            subheadline: 'R$ 15.000/mês até R$ 55.000 (Plano Enterprise)'
          }
        },
        {
          id: 'variant_a',
          name: 'Variant A - Urgency Focus',
          weight: 50,
          config: {
            headline: '🚨 ÚLTIMAS 47 VAGAS: Renda R$ 15K+ Indicando IA',
            subheadline: 'Sistema que Paga até R$ 55.000 por Empresa (Enterprise)'
          }
        }
      ]
    });

    // CTA Button Test
    this.addTest({
      id: 'cta_button',
      name: 'CTA Button Test',
      description: 'Test different CTA button texts',
      isActive: true,
      startDate: new Date(),
      variants: [
        {
          id: 'control',
          name: 'Control - Download Guide',
          weight: 33,
          config: {
            text: '📚 BAIXAR GUIA GRATUITO',
            color: 'primary'
          }
        },
        {
          id: 'variant_a',
          name: 'Variant A - Start Earning',
          weight: 33,
          config: {
            text: '💰 COMEÇAR A GANHAR R$ 15K',
            color: 'secondary'
          }
        },
        {
          id: 'variant_b',
          name: 'Variant B - Get Access',
          weight: 34,
          config: {
            text: '🚀 GARANTIR ACESSO VIP',
            color: 'primary'
          }
        }
      ]
    });

    // Social Proof Test
    this.addTest({
      id: 'social_proof',
      name: 'Social Proof Test',
      description: 'Test different social proof elements',
      isActive: true,
      startDate: new Date(),
      variants: [
        {
          id: 'control',
          name: 'Control - Testimonials',
          weight: 50,
          config: {
            type: 'testimonials',
            showNumbers: true,
            showPhotos: false
          }
        },
        {
          id: 'variant_a',
          name: 'Variant A - Live Counter',
          weight: 50,
          config: {
            type: 'live_counter',
            showNumbers: true,
            showPhotos: true
          }
        }
      ]
    });

    // Form Fields Test
    this.addTest({
      id: 'form_fields',
      name: 'Form Fields Test',
      description: 'Test different form field combinations',
      isActive: true,
      startDate: new Date(),
      variants: [
        {
          id: 'control',
          name: 'Control - Name + Email',
          weight: 50,
          config: {
            fields: ['name', 'email'],
            showNicheSelector: false
          }
        },
        {
          id: 'variant_a',
          name: 'Variant A - Email Only + Niche',
          weight: 50,
          config: {
            fields: ['email'],
            showNicheSelector: true
          }
        }
      ]
    });
  }

  // Add a new A/B test
  addTest(test: ABTest): void {
    this.tests.set(test.id, test);
  }

  // Get variant for a user and test
  getVariant(testId: string, userId: string): ABTestVariant | null {
    const test = this.tests.get(testId);
    if (!test || !test.isActive) return null;

    // Check if user already has a variant assigned
    const userTests = this.userVariants.get(userId);
    if (userTests?.has(testId)) {
      const variantId = userTests.get(testId)!;
      return test.variants.find(v => v.id === variantId) || null;
    }

    // Assign new variant based on weights
    const variant = this.assignVariant(test, userId);
    
    // Store assignment
    if (!this.userVariants.has(userId)) {
      this.userVariants.set(userId, new Map());
    }
    this.userVariants.get(userId)!.set(testId, variant.id);
    this.saveUserVariants();

    // Track assignment
    this.trackEvent({
      testId,
      variantId: variant.id,
      userId,
      eventType: 'view',
      timestamp: new Date()
    });

    return variant;
  }

  // Assign variant based on weights
  private assignVariant(test: ABTest, userId: string): ABTestVariant {
    // Use user ID as seed for consistent assignment
    const seed = this.hashString(userId + test.id);
    const random = (seed % 100) + 1; // 1-100

    let cumulativeWeight = 0;
    for (const variant of test.variants) {
      cumulativeWeight += variant.weight;
      if (random <= cumulativeWeight) {
        return variant;
      }
    }

    // Fallback to first variant
    return test.variants[0];
  }

  // Simple hash function for consistent randomization
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  // Track conversion event
  trackEvent(event: ConversionEvent): void {
    this.events.push(event);
    this.saveEvents();

    // Send to analytics
    if (window.gtag) {
      window.gtag('event', 'ab_test_event', {
        test_id: event.testId,
        variant_id: event.variantId,
        event_type: event.eventType,
        value: event.value || 0
      });
    }
  }

  // Track conversion for a test
  trackConversion(testId: string, userId: string, value?: number): void {
    const userTests = this.userVariants.get(userId);
    const variantId = userTests?.get(testId);
    
    if (variantId) {
      this.trackEvent({
        testId,
        variantId,
        userId,
        eventType: 'conversion',
        timestamp: new Date(),
        value
      });
    }
  }

  // Get test results
  getTestResults(testId: string): any {
    const test = this.tests.get(testId);
    if (!test) return null;

    const testEvents = this.events.filter(e => e.testId === testId);
    const results: any = {
      testId,
      testName: test.name,
      variants: []
    };

    for (const variant of test.variants) {
      const variantEvents = testEvents.filter(e => e.variantId === variant.id);
      const views = variantEvents.filter(e => e.eventType === 'view').length;
      const conversions = variantEvents.filter(e => e.eventType === 'conversion').length;
      const conversionRate = views > 0 ? (conversions / views) * 100 : 0;

      results.variants.push({
        id: variant.id,
        name: variant.name,
        views,
        conversions,
        conversionRate: conversionRate.toFixed(2) + '%',
        totalValue: variantEvents
          .filter(e => e.eventType === 'conversion')
          .reduce((sum, e) => sum + (e.value || 0), 0)
      });
    }

    return results;
  }

  // Get all active tests
  getActiveTests(): ABTest[] {
    return Array.from(this.tests.values()).filter(test => test.isActive);
  }

  // Stop a test
  stopTest(testId: string): void {
    const test = this.tests.get(testId);
    if (test) {
      test.isActive = false;
      test.endDate = new Date();
    }
  }

  // Get user ID (create if doesn't exist)
  getUserId(): string {
    let userId = localStorage.getItem('cro_user_id');
    if (!userId) {
      userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('cro_user_id', userId);
    }
    return userId;
  }

  // Save user variants to localStorage
  private saveUserVariants(): void {
    const data: any = {};
    this.userVariants.forEach((tests, userId) => {
      data[userId] = Object.fromEntries(tests);
    });
    localStorage.setItem('cro_user_variants', JSON.stringify(data));
  }

  // Load user variants from localStorage
  private loadUserVariants(): void {
    try {
      const data = localStorage.getItem('cro_user_variants');
      if (data) {
        const parsed = JSON.parse(data);
        Object.entries(parsed).forEach(([userId, tests]: [string, any]) => {
          this.userVariants.set(userId, new Map(Object.entries(tests)));
        });
      }
    } catch (error) {
      console.error('Error loading user variants:', error);
    }
  }

  // Save events to localStorage
  private saveEvents(): void {
    try {
      // Keep only last 1000 events to prevent storage bloat
      const recentEvents = this.events.slice(-1000);
      localStorage.setItem('cro_events', JSON.stringify(recentEvents));
    } catch (error) {
      console.error('Error saving events:', error);
    }
  }

  // Load events from localStorage
  private loadEvents(): void {
    try {
      const data = localStorage.getItem('cro_events');
      if (data) {
        this.events = JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading events:', error);
    }
  }
}

// Global CRO service instance
export const cro = new CROService();

// Helper hook for using A/B tests in components
export const useABTest = (testId: string) => {
  const userId = cro.getUserId();
  const variant = cro.getVariant(testId, userId);
  
  const trackConversion = (value?: number) => {
    cro.trackConversion(testId, userId, value);
  };

  return {
    variant: variant?.config || {},
    variantId: variant?.id || 'control',
    trackConversion
  };
};
