
# Please enter the commit message for your changes. Lines starting
# with '#' will be ignored, and an empty message aborts the commit.
#
# On branch main
# Your branch is up to date with 'origin/main'.
#
# Changes to be committed:
#	new file:   .env
#	new file:   .env.example
#	new file:   .env.production
#	new file:   SETUP-EMAIL.md
#	new file:   emergency.html
#	new file:   netlify.toml
#	new file:   netlify/functions/capture-lead.js
#	deleted:    package-lock.json
#	modified:   package.json
#	new file:   pdf/Domine-a-Nova-Economia-Digital.pdf
#	new file:   public/pdf/Domine-a-Nova-Economia-Digital.pdf
#	new file:   scripts/build-optimized.js
#	modified:   src/App.tsx
#	new file:   src/components/BenefitsSection.tsx
#	new file:   src/components/BrevoStatus.tsx
#	new file:   src/components/EmailDebugPanel.tsx
#	new file:   src/components/EmailPreview.tsx
#	new file:   src/components/ExitIntentPopup.tsx
#	new file:   src/components/FAQSection.tsx
#	modified:   src/components/HeroSection.tsx
#	new file:   src/components/LeadCaptureForm.tsx
#	new file:   src/components/MasterEmailSystem.tsx
#	new file:   src/components/SimpleDebug.tsx
#	modified:   src/components/StepContent.tsx
#	new file:   src/components/UrgencySection.tsx
#	modified:   src/components/WelcomeStep.tsx
#	new file:   src/core/config/index.ts
#	new file:   src/core/types/index.ts
#	new file:   src/core/utils/logger.ts
#	new file:   src/design-system/components/Button.tsx
#	new file:   src/design-system/components/Input.tsx
#	new file:   src/design-system/components/Layout.tsx
#	new file:   src/design-system/index.ts
#	new file:   src/design-system/tokens.ts
#	new file:   src/hooks/useAnalytics.ts
#	new file:   src/hooks/useExitIntent.ts
#	modified:   src/pages/Index.tsx
#	new file:   src/services/aiPersonalization.ts
#	new file:   src/services/analytics.ts
#	new file:   src/services/automationEngine.ts
#	new file:   src/services/brevoService.ts
#	new file:   src/services/cro.ts
#	new file:   src/services/emailFallback.ts
#	new file:   src/services/emailMarketing.ts
#	new file:   src/services/integrations.ts
#	new file:   src/services/leadCapture.ts
#	modified:   src/utils/animations.ts
#	new file:   test-brevo.js
#	new file:   test-email-direct.js
#	modified:   vite.config.ts
#	new file:   vite.config.ts.bak
#
