import React from "react";

const App = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '400px',
        width: '100%',
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        borderRadius: '20px',
        padding: '30px',
        color: 'white',
        textAlign: 'center'
      }}>
        <h1 style={{ fontSize: '28px', marginBottom: '10px' }}>🚀 GRIP Afiliados</h1>
        <p style={{ marginBottom: '30px', opacity: 0.8 }}>Sistema funcionando na porta 3000!</p>

        <div style={{ textAlign: 'left', marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '8px' }}>Nome:</label>
          <input
            type="text"
            placeholder="Seu nome completo"
            style={{
              width: '100%',
              padding: '12px',
              borderRadius: '10px',
              border: '1px solid rgba(255,255,255,0.3)',
              background: 'rgba(255,255,255,0.1)',
              color: 'white',
              marginBottom: '15px'
            }}
          />

          <label style={{ display: 'block', marginBottom: '8px' }}>Email:</label>
          <input
            type="email"
            placeholder="<EMAIL>"
            style={{
              width: '100%',
              padding: '12px',
              borderRadius: '10px',
              border: '1px solid rgba(255,255,255,0.3)',
              background: 'rgba(255,255,255,0.1)',
              color: 'white',
              marginBottom: '15px'
            }}
          />

          <label style={{ display: 'block', marginBottom: '8px' }}>WhatsApp:</label>
          <input
            type="tel"
            placeholder="(11) 99999-9999"
            style={{
              width: '100%',
              padding: '12px',
              borderRadius: '10px',
              border: '1px solid rgba(255,255,255,0.3)',
              background: 'rgba(255,255,255,0.1)',
              color: 'white',
              marginBottom: '20px'
            }}
          />
        </div>

        <button
          onClick={() => {
            alert('🎉 Sistema funcionando na porta 3000! ✅\n\n🔗 Link GRIP: https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368');
            console.log('🎉 Formulário funcionando perfeitamente na porta 3000!');
          }}
          style={{
            width: '100%',
            padding: '15px',
            background: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '10px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer',
            marginBottom: '20px'
          }}
        >
          🚀 QUERO MEU LINK DE AFILIADO
        </button>

        <div style={{ fontSize: '14px', opacity: 0.7 }}>
          <p>✅ Localhost:3000 funcionando!</p>
          <p>🔗 GRIP: grip.gaiodataos.com</p>
        </div>
      </div>
    </div>
  );
};

export default App;
