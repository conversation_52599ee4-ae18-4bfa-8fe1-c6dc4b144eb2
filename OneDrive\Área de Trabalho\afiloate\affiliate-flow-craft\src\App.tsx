import React from "react";

const App = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white/10 backdrop-blur-lg rounded-3xl p-8 text-white">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">🚀</span>
          </div>
          <h1 className="text-2xl font-bold mb-2">GRIP Afiliados</h1>
          <p className="text-white/80 text-sm">Sistema funcionando na porta 3000!</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Nome Completo</label>
            <input
              type="text"
              className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30"
              placeholder="Seu nome completo"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Email</label>
            <input
              type="email"
              className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">WhatsApp</label>
            <input
              type="tel"
              className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30"
              placeholder="(11) 99999-9999"
            />
          </div>

          <button
            onClick={() => {
              alert('Sistema funcionando na porta 3000! ✅');
              console.log('🎉 Formulário funcionando perfeitamente!');
            }}
            className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            🚀 QUERO MEU LINK DE AFILIADO
          </button>
        </div>

        <div className="mt-6 text-center text-sm text-white/70">
          <p>✅ Localhost:3000 funcionando!</p>
          <p>🔗 Link: https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368</p>
        </div>
      </div>
    </div>
  );
};

export default App;
