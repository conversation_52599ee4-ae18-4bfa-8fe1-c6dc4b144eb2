// ===================================================================
// BUTTON COMPONENT - AffiliateFlow Pro Design System
// Enterprise-grade button with variants, states, and accessibility
// ===================================================================

import React, { forwardRef, ButtonHTMLAttributes } from 'react';
import { createLogger, logPerformance } from '../../core/utils/logger';
import { theme } from '../tokens';

const buttonLogger = createLogger('BUTTON_COMPONENT');

// Button Variant Types
export type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'outline' 
  | 'ghost' 
  | 'link' 
  | 'destructive';

export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export type ButtonState = 'default' | 'loading' | 'disabled' | 'success' | 'error';

// Button Props Interface
export interface ButtonProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'size'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  state?: ButtonState;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loadingText?: string;
  children: React.ReactNode;
  'data-testid'?: string;
}

// Variant Styles - Optimized for conversion
const variantStyles: Record<ButtonVariant, string> = {
  primary: `
    bg-gradient-to-r from-blue-600 to-blue-700 
    hover:from-blue-700 hover:to-blue-800 
    active:from-blue-800 active:to-blue-900
    text-white font-semibold
    shadow-lg hover:shadow-xl
    transform hover:scale-105 active:scale-95
    transition-all duration-200 ease-out
    border-0
    focus:ring-4 focus:ring-blue-500/50
  `,
  
  secondary: `
    bg-gradient-to-r from-orange-500 to-orange-600 
    hover:from-orange-600 hover:to-orange-700 
    active:from-orange-700 active:to-orange-800
    text-white font-semibold
    shadow-lg hover:shadow-xl
    transform hover:scale-105 active:scale-95
    transition-all duration-200 ease-out
    border-0
    focus:ring-4 focus:ring-orange-500/50
  `,
  
  outline: `
    bg-transparent 
    border-2 border-blue-600 
    hover:bg-blue-600 hover:border-blue-600
    text-blue-600 hover:text-white
    font-medium
    transition-all duration-200 ease-out
    focus:ring-4 focus:ring-blue-500/50
  `,
  
  ghost: `
    bg-transparent 
    hover:bg-gray-100 
    active:bg-gray-200
    text-gray-700 hover:text-gray-900
    font-medium
    border-0
    transition-all duration-200 ease-out
    focus:ring-4 focus:ring-gray-500/50
  `,
  
  link: `
    bg-transparent 
    text-blue-600 hover:text-blue-800
    underline hover:no-underline
    font-medium
    border-0 p-0
    transition-colors duration-200
    focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2
  `,
  
  destructive: `
    bg-gradient-to-r from-red-600 to-red-700 
    hover:from-red-700 hover:to-red-800 
    active:from-red-800 active:to-red-900
    text-white font-semibold
    shadow-lg hover:shadow-xl
    transform hover:scale-105 active:scale-95
    transition-all duration-200 ease-out
    border-0
    focus:ring-4 focus:ring-red-500/50
  `,
};

// Size Styles
const sizeStyles: Record<ButtonSize, string> = {
  xs: 'px-2 py-1 text-xs rounded-md min-h-[24px]',
  sm: 'px-3 py-1.5 text-sm rounded-md min-h-[32px]',
  md: 'px-4 py-2 text-base rounded-lg min-h-[40px]',
  lg: 'px-6 py-3 text-lg rounded-lg min-h-[48px]',
  xl: 'px-8 py-4 text-xl rounded-xl min-h-[56px]',
};

// State Styles
const stateStyles: Record<ButtonState, string> = {
  default: '',
  loading: 'cursor-wait opacity-80',
  disabled: 'cursor-not-allowed opacity-50 pointer-events-none',
  success: 'bg-green-600 hover:bg-green-700 text-white',
  error: 'bg-red-600 hover:bg-red-700 text-white',
};

// Loading Spinner Component
const LoadingSpinner: React.FC<{ size: ButtonSize }> = ({ size }) => {
  const spinnerSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7',
  };

  return (
    <svg
      className={`animate-spin ${spinnerSizes[size]}`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

// Success Icon Component
const SuccessIcon: React.FC<{ size: ButtonSize }> = ({ size }) => {
  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7',
  };

  return (
    <svg
      className={iconSizes[size]}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M5 13l4 4L19 7"
      />
    </svg>
  );
};

// Main Button Component
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      state = 'default',
      fullWidth = false,
      leftIcon,
      rightIcon,
      loadingText,
      children,
      className = '',
      onClick,
      disabled,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    // Determine if button is disabled
    const isDisabled = disabled || state === 'disabled' || state === 'loading';

    // Handle click with performance logging
    const handleClick = logPerformance('button.click')(
      (event: React.MouseEvent<HTMLButtonElement>) => {
        if (isDisabled) return;
        
        buttonLogger.debug('Button clicked', {
          variant,
          size,
          state,
          testId,
        });

        onClick?.(event);
      }
    );

    // Build CSS classes
    const baseClasses = `
      inline-flex items-center justify-center
      font-medium text-center
      focus:outline-none focus:ring-offset-2
      disabled:cursor-not-allowed disabled:opacity-50
      transition-all duration-200 ease-out
      select-none
    `;

    const classes = [
      baseClasses,
      variantStyles[variant],
      sizeStyles[size],
      stateStyles[state],
      fullWidth ? 'w-full' : '',
      className,
    ]
      .filter(Boolean)
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Render content based on state
    const renderContent = () => {
      if (state === 'loading') {
        return (
          <>
            <LoadingSpinner size={size} />
            <span className="ml-2">{loadingText || 'Loading...'}</span>
          </>
        );
      }

      if (state === 'success') {
        return (
          <>
            <SuccessIcon size={size} />
            <span className="ml-2">{children}</span>
          </>
        );
      }

      return (
        <>
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          <span>{children}</span>
          {rightIcon && <span className="ml-2">{rightIcon}</span>}
        </>
      );
    };

    return (
      <button
        ref={ref}
        type="button"
        className={classes}
        disabled={isDisabled}
        onClick={handleClick}
        data-testid={testId}
        aria-disabled={isDisabled}
        {...props}
      >
        {renderContent()}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Button Group Component for related actions
export interface ButtonGroupProps {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  spacing?: keyof typeof theme.spacing;
  className?: string;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  orientation = 'horizontal',
  spacing = '2',
  className = '',
}) => {
  const orientationClasses = {
    horizontal: `flex flex-row space-x-${spacing}`,
    vertical: `flex flex-col space-y-${spacing}`,
  };

  return (
    <div className={`${orientationClasses[orientation]} ${className}`}>
      {children}
    </div>
  );
};

// Icon Button Component for icon-only buttons
export interface IconButtonProps extends Omit<ButtonProps, 'children' | 'leftIcon' | 'rightIcon'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, size = 'md', ...props }, ref) => {
    const iconSizes = {
      xs: 'p-1',
      sm: 'p-1.5',
      md: 'p-2',
      lg: 'p-3',
      xl: 'p-4',
    };

    return (
      <Button
        ref={ref}
        size={size}
        className={`${iconSizes[size]} aspect-square`}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// Export all button-related components and types
export default Button;
export type { ButtonVariant, ButtonSize, ButtonState };
