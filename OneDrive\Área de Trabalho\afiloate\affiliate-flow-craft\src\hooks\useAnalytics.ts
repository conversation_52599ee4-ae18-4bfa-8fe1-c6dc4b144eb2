// Custom Hook for Analytics Integration
import { useEffect, useRef } from 'react';
import { analytics } from '../services/analytics';

interface UseAnalyticsOptions {
  trackPageView?: boolean;
  trackScrollDepth?: boolean;
  trackTimeOnPage?: boolean;
  customData?: Record<string, any>;
}

export const useAnalytics = (
  pageName: string,
  options: UseAnalyticsOptions = {}
) => {
  const {
    trackPageView = true,
    trackScrollDepth = true,
    trackTimeOnPage = true,
    customData = {}
  } = options;

  const startTime = useRef<number>(Date.now());
  const scrollTracked = useRef<Set<number>>(new Set());

  useEffect(() => {
    // Track page view
    if (trackPageView) {
      analytics.trackPageView(pageName, customData);
    }

    // Track scroll depth
    const handleScroll = () => {
      if (!trackScrollDepth) return;

      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = Math.round((scrollTop / docHeight) * 100);

      if (scrollPercent >= 0 && !scrollTracked.current.has(scrollPercent)) {
        analytics.trackScrollDepth(scrollPercent);
        scrollTracked.current.add(scrollPercent);
      }
    };

    // Track time on page
    const timeInterval = setInterval(() => {
      if (trackTimeOnPage) {
        const timeSpent = Math.floor((Date.now() - startTime.current) / 1000);
        analytics.trackTimeOnPage(timeSpent);
      }
    }, 60000); // Every minute

    // Add event listeners
    if (trackScrollDepth) {
      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    // Cleanup
    return () => {
      if (trackScrollDepth) {
        window.removeEventListener('scroll', handleScroll);
      }
      clearInterval(timeInterval);
    };
  }, [pageName, trackPageView, trackScrollDepth, trackTimeOnPage, customData]);

  // Return tracking functions for manual use
  return {
    trackEvent: analytics.trackEvent.bind(analytics),
    trackLeadCapture: analytics.trackLeadCapture.bind(analytics),
    trackConversion: analytics.trackConversion.bind(analytics),
    trackButtonClick: analytics.trackButtonClick.bind(analytics),
    trackFormInteraction: analytics.trackFormInteraction.bind(analytics),
    getUTMParameters: analytics.getUTMParameters.bind(analytics)
  };
};

// Hook for tracking button clicks automatically
export const useButtonTracking = (
  buttonName: string,
  location: string,
  customData?: Record<string, any>
) => {
  const handleClick = () => {
    analytics.trackButtonClick(buttonName, location, customData);
  };

  return { onClick: handleClick };
};

// Hook for tracking form interactions
export const useFormTracking = (formName: string) => {
  const trackFieldFocus = (fieldName: string) => {
    analytics.trackFormInteraction(formName, 'field_focus', fieldName);
  };

  const trackFieldBlur = (fieldName: string) => {
    analytics.trackFormInteraction(formName, 'field_blur', fieldName);
  };

  const trackFormSubmit = () => {
    analytics.trackFormInteraction(formName, 'form_submit');
  };

  const trackFormError = (fieldName: string, error: string) => {
    analytics.trackFormInteraction(formName, 'form_error', `${fieldName}: ${error}`);
  };

  return {
    trackFieldFocus,
    trackFieldBlur,
    trackFormSubmit,
    trackFormError
  };
};

// Hook for A/B testing
export const useABTest = (testName: string, variants: string[]) => {
  const getVariant = (): string => {
    // Check if variant is already stored
    const storedVariant = localStorage.getItem(`ab_test_${testName}`);
    if (storedVariant && variants.includes(storedVariant)) {
      return storedVariant;
    }

    // Assign random variant
    const randomVariant = variants[Math.floor(Math.random() * variants.length)];
    localStorage.setItem(`ab_test_${testName}`, randomVariant);

    // Track assignment
    analytics.trackEvent({
      event: 'ABTest',
      category: 'A/B Testing',
      action: 'variant_assigned',
      label: `${testName}: ${randomVariant}`,
      customParameters: {
        testName,
        variant: randomVariant
      }
    });

    return randomVariant;
  };

  const trackConversion = (conversionType: string) => {
    const variant = getVariant();
    analytics.trackEvent({
      event: 'ABTestConversion',
      category: 'A/B Testing',
      action: 'conversion',
      label: `${testName}: ${variant} - ${conversionType}`,
      customParameters: {
        testName,
        variant,
        conversionType
      }
    });
  };

  return {
    variant: getVariant(),
    trackConversion
  };
};

// Hook for exit intent tracking
export const useExitIntent = (callback: () => void) => {
  useEffect(() => {
    let hasTriggered = false;

    const handleMouseLeave = (e: MouseEvent) => {
      if (e.clientY <= 0 && !hasTriggered) {
        hasTriggered = true;
        analytics.trackEvent({
          event: 'ExitIntent',
          category: 'User Behavior',
          action: 'exit_intent_detected',
          label: window.location.pathname
        });
        callback();
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [callback]);
};

// Hook for conversion funnel tracking
export const useConversionFunnel = (funnelName: string) => {
  const trackStep = (stepName: string, stepNumber: number, customData?: Record<string, any>) => {
    analytics.trackEvent({
      event: 'FunnelStep',
      category: 'Conversion Funnel',
      action: 'step_completed',
      label: `${funnelName} - Step ${stepNumber}: ${stepName}`,
      value: stepNumber,
      customParameters: {
        funnelName,
        stepName,
        stepNumber,
        ...customData
      }
    });
  };

  const trackDropoff = (stepName: string, stepNumber: number, reason?: string) => {
    analytics.trackEvent({
      event: 'FunnelDropoff',
      category: 'Conversion Funnel',
      action: 'step_dropoff',
      label: `${funnelName} - Step ${stepNumber}: ${stepName}`,
      value: stepNumber,
      customParameters: {
        funnelName,
        stepName,
        stepNumber,
        reason
      }
    });
  };

  return {
    trackStep,
    trackDropoff
  };
};

// Hook for real-time user behavior
export const useUserBehavior = () => {
  useEffect(() => {
    let clickCount = 0;
    let lastActivity = Date.now();

    const trackActivity = () => {
      lastActivity = Date.now();
      clickCount++;

      // Track high engagement users
      if (clickCount === 10) {
        analytics.trackEvent({
          event: 'HighEngagement',
          category: 'User Behavior',
          action: 'high_engagement_user',
          label: '10+ clicks',
          value: clickCount
        });
      }
    };

    // Track inactivity
    const inactivityTimer = setInterval(() => {
      const timeSinceActivity = Date.now() - lastActivity;
      if (timeSinceActivity > 300000) { // 5 minutes
        analytics.trackEvent({
          event: 'Inactivity',
          category: 'User Behavior',
          action: 'user_inactive',
          label: '5+ minutes inactive',
          value: Math.floor(timeSinceActivity / 1000)
        });
      }
    }, 60000);

    document.addEventListener('click', trackActivity);
    document.addEventListener('scroll', trackActivity);
    document.addEventListener('keypress', trackActivity);

    return () => {
      document.removeEventListener('click', trackActivity);
      document.removeEventListener('scroll', trackActivity);
      document.removeEventListener('keypress', trackActivity);
      clearInterval(inactivityTimer);
    };
  }, []);
};
