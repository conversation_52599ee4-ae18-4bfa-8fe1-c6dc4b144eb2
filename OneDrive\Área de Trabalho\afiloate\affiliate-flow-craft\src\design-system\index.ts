// ===================================================================
// DESIGN SYSTEM EXPORTS - AffiliateFlow Pro
// Central export file for all design system components and tokens
// ===================================================================

// Design Tokens
export * from './tokens';

// Layout Components
export {
  Container,
  Grid,
  Flex,
  Stack,
  Section,
  Card,
  ShowAt,
  HideAt,
  AspectRatio,
  Center,
  Spacer,
  useBreakpoint,
} from './components/Layout';

// Form Components
export {
  default as Input,
  Textarea,
  InputGroup,
} from './components/Input';
export type {
  InputProps,
  TextareaProps,
  InputGroupProps,
  InputVariant,
  InputSize,
  InputState,
} from './components/Input';

// Button Components
export {
  default as Button,
  ButtonGroup,
  IconButton,
} from './components/Button';
export type {
  ButtonProps,
  ButtonGroupProps,
  IconButtonProps,
  ButtonVariant,
  ButtonSize,
  ButtonState,
} from './components/Button';

// Theme and Utilities
export {
  theme,
  useTheme,
  generateCSSCustomProperties,
  BASE_COLORS,
  SEMANTIC_COLORS,
  TYPOGRAPHY,
  SPACING,
  BORDER_RADIUS,
  SHADOWS,
  ANIMATIONS,
  BREAKPOINTS,
  Z_INDEX,
} from './tokens';

// Design System Version
export const DESIGN_SYSTEM_VERSION = '1.0.0';

// Design System Metadata
export const DESIGN_SYSTEM_INFO = {
  name: 'AffiliateFlow Pro Design System',
  version: DESIGN_SYSTEM_VERSION,
  description: 'Enterprise-grade design system for high-converting affiliate marketing applications',
  author: 'AffiliateFlow Pro Team',
  license: 'Proprietary',
  repository: 'https://github.com/affiliateflow-pro/design-system',
  documentation: 'https://design.affiliateflowpro.com',
  components: [
    'Button',
    'Input',
    'Textarea',
    'Container',
    'Grid',
    'Flex',
    'Stack',
    'Section',
    'Card',
    'AspectRatio',
    'Center',
    'Spacer',
  ],
  tokens: [
    'Colors',
    'Typography',
    'Spacing',
    'Border Radius',
    'Shadows',
    'Animations',
    'Breakpoints',
    'Z-Index',
  ],
} as const;
