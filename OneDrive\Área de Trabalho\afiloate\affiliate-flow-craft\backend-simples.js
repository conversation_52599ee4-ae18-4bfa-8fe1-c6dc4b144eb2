// ===================================================================
// BACKEND SIMPLES E ROBUSTO - SOLUÇÃO DEFINITIVA
// Node.js + Express + Nodemailer para envio de emails
// ===================================================================

const express = require('express');
const cors = require('cors');
const nodemailer = require('nodemailer');
const path = require('path');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('dist'));

// Configuração do Nodemailer (usando serviço gratuito)
const transporter = nodemailer.createTransport({
  host: 'smtp.ethereal.email',
  port: 587,
  auth: {
    user: '<EMAIL>',
    pass: 'ethereal.pass'
  }
});

// Função para simular envio de email (para demonstração)
async function enviarEmailSimulado(emailData) {
  console.log('📧 SIMULANDO ENVIO DE EMAIL:');
  console.log('Para:', emailData.to);
  console.log('Assunto:', emailData.subject);
  console.log('✅ Email "enviado" com sucesso!');

  return {
    messageId: `sim_${Date.now()}@localhost`,
    response: 'Email simulado enviado com sucesso'
  };
}

// Rota para envio de email
app.post('/api/send-email', async (req, res) => {
  console.log('🚀 BACKEND ROBUSTO: Recebendo requisição...');
  
  try {
    const { name, email, phone, source } = req.body;
    
    console.log('📝 Dados recebidos:', { name, email, phone, source });
    
    // Validação básica
    if (!name || !email) {
      return res.status(400).json({
        success: false,
        message: 'Nome e email são obrigatórios'
      });
    }
    
    // Email para você (notificação)
    const emailParaVoce = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: '🎉 Novo Lead GRIP - Backend Funcionando!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background: #f8f9fa;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 15px; color: white; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎉 Novo Lead Capturado!</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Backend funcionando perfeitamente!</p>
          </div>
          
          <div style="background: white; padding: 30px; border-radius: 15px; margin-top: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <h2 style="color: #2c3e50; margin-top: 0;">📋 Dados do Lead:</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px; font-weight: bold; color: #555;">👤 Nome:</td>
                <td style="padding: 10px; color: #333;">${name}</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px; font-weight: bold; color: #555;">📧 Email:</td>
                <td style="padding: 10px; color: #333;">${email}</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px; font-weight: bold; color: #555;">📱 Telefone:</td>
                <td style="padding: 10px; color: #333;">${phone || 'Não informado'}</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px; font-weight: bold; color: #555;">📊 Fonte:</td>
                <td style="padding: 10px; color: #333;">${source || 'Backend'}</td>
              </tr>
              <tr>
                <td style="padding: 10px; font-weight: bold; color: #555;">⏰ Data:</td>
                <td style="padding: 10px; color: #333;">${new Date().toLocaleString('pt-BR')}</td>
              </tr>
            </table>
            
            <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 10px;">
              <h3 style="color: #2c3e50; margin-top: 0;">🔗 Links Importantes:</h3>
              <p><strong>Afiliado GRIP:</strong><br>
              <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368" style="color: #4CAF50;">https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368</a></p>
              
              <p><strong>Apps:</strong><br>
              <a href="https://play.google.com/store/apps/details?id=com.gaiodataos.grip" style="color: #4CAF50;">Android</a> | 
              <a href="https://apps.apple.com/us/app/grip-gaiodataos/id6743857628" style="color: #4CAF50;">iOS</a></p>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666;">
            <p>✅ Backend Node.js funcionando perfeitamente!</p>
          </div>
        </div>
      `
    };
    
    // Email para o lead (resposta automática)
    const emailParaLead = {
      from: '<EMAIL>',
      to: email,
      subject: `🎉 Bem-vindo ${name.split(' ')[0]} - Programa GRIP`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #f8f9fa;">
          
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; border-radius: 15px 15px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 32px; font-weight: bold;">🎉 Parabéns, ${name.split(' ')[0]}!</h1>
            <p style="color: rgba(255,255,255,0.9); margin: 15px 0 0 0; font-size: 18px;">Você agora faz parte do seleto grupo que gera <strong>RENDA RECORRENTE</strong> indicando IA para empresas!</p>
          </div>

          <div style="background: white; padding: 40px 30px;">
            
            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 15px; padding: 25px; margin-bottom: 30px; color: white;">
              <h2 style="margin-top: 0; font-size: 24px;">🤖 SOBRE A GRIP - PLATAFORMA DE IA EMPRESARIAL</h2>
              <ul style="list-style: none; padding: 0; margin: 0;">
                <li style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                  ✅ <strong>Soluções de IA financeira</strong> para empresas de todos os portes
                </li>
                <li style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                  ✅ <strong>Planos de R$ 15.000/mês</strong> até R$ 55.000 (Enterprise)
                </li>
                <li style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                  ✅ <strong>Comissões recorrentes</strong> para afiliados qualificados
                </li>
              </ul>
            </div>

            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 15px; padding: 25px; margin-bottom: 30px; color: white;">
              <h2 style="margin-top: 0; font-size: 24px;">💰 SEU POTENCIAL DE GANHOS</h2>
              <div style="display: grid; gap: 15px;">
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                  <strong style="font-size: 18px;">Base: R$ 15.000/mês</strong><br>
                  <span style="opacity: 0.9;">Por cada empresa que você indicar e fechar contrato</span>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                  <strong style="font-size: 18px;">Enterprise: R$ 55.000</strong><br>
                  <span style="opacity: 0.9;">Quando empresa contrata plano premium</span>
                </div>
              </div>
            </div>

            <div style="text-align: center; margin: 40px 0;">
              <h2 style="color: #2c3e50; margin-bottom: 20px;">🔗 SEUS PRÓXIMOS PASSOS</h2>
              
              <div style="margin-bottom: 20px;">
                <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368" 
                   style="display: inline-block; background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 18px 35px; text-decoration: none; border-radius: 12px; font-weight: bold; font-size: 18px;">
                  🚀 ACESSAR SEU LINK DE AFILIADO
                </a>
              </div>

              <p style="color: #666; margin: 20px 0;">Baixe o app GRIP:</p>
              
              <div>
                <a href="https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share"
                   style="display: inline-block; background: #34A853; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 0 10px 10px 0; font-weight: bold;">
                  📱 Android
                </a>
                <a href="https://apps.apple.com/us/app/grip-gaiodataos/id6743857628"
                   style="display: inline-block; background: #007AFF; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 0 0 10px 10px; font-weight: bold;">
                  📱 iOS
                </a>
              </div>
            </div>

          </div>

          <div style="background: #2c3e50; color: white; padding: 30px; text-align: center; border-radius: 0 0 15px 15px;">
            <p style="margin: 0; font-size: 16px;">
              Sucesso e bons ganhos! 💪<br>
              <strong>Equipe AffiliateFlow Pro</strong>
            </p>
          </div>

        </div>
      `
    };
    
    // Enviar emails (simulado para demonstração)
    console.log('📧 Enviando emails...');

    await enviarEmailSimulado(emailParaVoce);
    console.log('✅ Email para você enviado!');

    await enviarEmailSimulado(emailParaLead);
    console.log('✅ Email para lead enviado!');
    
    // Resposta de sucesso
    res.json({
      success: true,
      message: 'Emails enviados com sucesso!',
      data: {
        leadId: `backend_${Date.now()}`,
        affiliateLink: 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368',
        emailSent: true,
        method: 'Backend Node.js'
      }
    });
    
  } catch (error) {
    console.error('❌ Erro no backend:', error);
    
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

// Rota para servir o frontend
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log('🚀 BACKEND ROBUSTO INICIADO!');
  console.log(`✅ Servidor rodando em: http://localhost:${PORT}`);
  console.log('📧 Sistema de email configurado');
  console.log('🔧 CORS habilitado');
  console.log('💪 Pronto para receber leads!');
});

module.exports = app;
