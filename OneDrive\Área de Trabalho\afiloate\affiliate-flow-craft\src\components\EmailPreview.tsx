import React, { useState } from 'react';
import { Mail, Eye, Send, Users, Target, Zap } from 'lucide-react';
import { EMAIL_TEMPLATES } from '../services/emailMarketing';

const EmailPreview: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<keyof typeof EMAIL_TEMPLATES>('WELCOME');
  const [selectedNiche, setSelectedNiche] = useState('tech');

  const sampleData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    affiliateLink: 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368'
  };

  const processTemplate = (content: string) => {
    return content
      .replace(/{{name}}/g, sampleData.name)
      .replace(/{{affiliateLink}}/g, sampleData.affiliateLink);
  };

  const templates = [
    { key: 'WELCOME', label: '🎉 Boas-vindas', description: 'Email imediato após captura' },
    { key: 'TECH_NICHE', label: '💻 Kit Tech', description: 'Materiais para influencers tech' },
    { key: 'FINANCE_NICHE', label: '💰 Kit Finanças', description: 'Materiais para influencers de finanças' },
    { key: 'BUSINESS_NICHE', label: '🚀 Kit Business', description: 'Materiais para influencers business' }
  ];

  return (
    <div className="glass-dark rounded-3xl p-8 max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <div className="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
          <Mail className="w-8 h-8 text-white" />
        </div>
        
        <h3 className="text-2xl font-bold text-lp-light mb-2">
          📧 Sistema de Email Marketing Automatizado
        </h3>
        
        <p className="text-lp-light/80">
          Emails personalizados por nicho enviados automaticamente para cada lead
        </p>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="glass rounded-2xl p-6 text-center">
          <div className="w-12 h-12 bg-lp-green rounded-full flex items-center justify-center mx-auto mb-3">
            <Send className="w-6 h-6 text-white" />
          </div>
          <div className="text-2xl font-bold text-lp-light mb-1">200</div>
          <div className="text-sm text-lp-light/70">Emails/mês grátis</div>
        </div>

        <div className="glass rounded-2xl p-6 text-center">
          <div className="w-12 h-12 bg-lp-blue rounded-full flex items-center justify-center mx-auto mb-3">
            <Target className="w-6 h-6 text-white" />
          </div>
          <div className="text-2xl font-bold text-lp-light mb-1">4</div>
          <div className="text-sm text-lp-light/70">Nichos segmentados</div>
        </div>

        <div className="glass rounded-2xl p-6 text-center">
          <div className="w-12 h-12 bg-lp-orange rounded-full flex items-center justify-center mx-auto mb-3">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div className="text-2xl font-bold text-lp-light mb-1">Auto</div>
          <div className="text-sm text-lp-light/70">Sequência automática</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Seletor de Templates */}
        <div>
          <h4 className="text-lg font-bold text-lp-light mb-4">
            📋 Templates Disponíveis
          </h4>
          
          <div className="space-y-3">
            {templates.map((template) => (
              <button
                key={template.key}
                onClick={() => setSelectedTemplate(template.key as keyof typeof EMAIL_TEMPLATES)}
                className={`w-full text-left p-4 rounded-xl border-2 transition-all ${
                  selectedTemplate === template.key
                    ? 'border-lp-blue bg-lp-blue/20'
                    : 'border-white/20 bg-lp-navy/30 hover:border-white/40'
                }`}
              >
                <div className="font-medium text-lp-light mb-1">
                  {template.label}
                </div>
                <div className="text-sm text-lp-light/60">
                  {template.description}
                </div>
              </button>
            ))}
          </div>

          {/* Fluxo de Automação */}
          <div className="mt-8">
            <h4 className="text-lg font-bold text-lp-light mb-4">
              ⚡ Fluxo de Automação
            </h4>
            
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-8 h-8 bg-lp-green rounded-full flex items-center justify-center text-white text-sm font-bold">
                  1
                </div>
                <div>
                  <div className="font-medium text-lp-light">Lead capturado</div>
                  <div className="text-sm text-lp-light/60">Email de boas-vindas enviado imediatamente</div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-8 h-8 bg-lp-blue rounded-full flex items-center justify-center text-white text-sm font-bold">
                  2
                </div>
                <div>
                  <div className="font-medium text-lp-light">2 horas depois</div>
                  <div className="text-sm text-lp-light/60">Kit personalizado por nicho</div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-8 h-8 bg-lp-orange rounded-full flex items-center justify-center text-white text-sm font-bold">
                  3
                </div>
                <div>
                  <div className="font-medium text-lp-light">24 horas</div>
                  <div className="text-sm text-lp-light/60">Follow-up com cases de sucesso</div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-8 h-8 bg-lp-purple rounded-full flex items-center justify-center text-white text-sm font-bold">
                  4
                </div>
                <div>
                  <div className="font-medium text-lp-light">7 dias</div>
                  <div className="text-sm text-lp-light/60">Convite para grupo VIP</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Preview do Email */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-bold text-lp-light">
              👁️ Preview do Email
            </h4>
            <div className="flex items-center gap-2 text-sm text-lp-light/60">
              <Eye className="w-4 h-4" />
              <span>Visualização</span>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 text-gray-900 max-h-96 overflow-y-auto">
            <div className="border-b border-gray-200 pb-4 mb-4">
              <div className="text-sm text-gray-600 mb-2">
                <strong>Para:</strong> {sampleData.email}
              </div>
              <div className="text-lg font-bold text-gray-900">
                {EMAIL_TEMPLATES[selectedTemplate].subject}
              </div>
            </div>

            <div className="whitespace-pre-line text-sm leading-relaxed">
              {processTemplate(EMAIL_TEMPLATES[selectedTemplate].content)}
            </div>
          </div>

          {/* Métricas Esperadas */}
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="glass rounded-xl p-4 text-center">
              <div className="text-lg font-bold text-lp-green mb-1">35%</div>
              <div className="text-xs text-lp-light/60">Taxa de abertura</div>
            </div>
            <div className="glass rounded-xl p-4 text-center">
              <div className="text-lg font-bold text-lp-blue mb-1">12%</div>
              <div className="text-xs text-lp-light/60">Taxa de clique</div>
            </div>
          </div>
        </div>
      </div>

      {/* Configuração */}
      <div className="mt-8 p-6 bg-lp-navy/50 rounded-2xl border border-white/10">
        <h4 className="text-lg font-bold text-lp-light mb-4">
          🔧 Configuração Necessária
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="font-medium text-lp-light mb-2">📧 EmailJS (Gratuito)</h5>
            <ul className="text-sm text-lp-light/70 space-y-1">
              <li>• 200 emails/mês grátis</li>
              <li>• Configuração em 5 minutos</li>
              <li>• Integração direta com Gmail</li>
            </ul>
          </div>
          
          <div>
            <h5 className="font-medium text-lp-light mb-2">📊 Airtable (Gratuito)</h5>
            <ul className="text-sm text-lp-light/70 space-y-1">
              <li>• 1.200 registros grátis</li>
              <li>• Interface visual intuitiva</li>
              <li>• API automática</li>
            </ul>
          </div>
        </div>

        <div className="mt-4 p-4 bg-lp-blue/20 rounded-xl border border-lp-blue/30">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-lp-blue rounded-full flex items-center justify-center">
              <Mail className="w-4 h-4 text-white" />
            </div>
            <div>
              <div className="font-medium text-lp-light">Guia de Configuração</div>
              <div className="text-sm text-lp-light/70">
                Consulte o arquivo <code>SETUP-EMAIL.md</code> para configurar em 10 minutos
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailPreview;
