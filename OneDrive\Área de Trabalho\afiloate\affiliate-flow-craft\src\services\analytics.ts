// Advanced Analytics and Tracking System
// Free solutions: Google Analytics 4, Facebook Pixel, Hotjar (free tier)

interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
}

interface ConversionData {
  leadId: string;
  email: string;
  source: string;
  niche?: string;
  value: number;
  currency: string;
}

class AnalyticsService {
  private gaId: string;
  private fbPixelId: string;
  private hotjarId: string;
  private isInitialized: boolean = false;

  constructor() {
    this.gaId = import.meta.env.VITE_GA_MEASUREMENT_ID || '';
    this.fbPixelId = import.meta.env.VITE_FACEBOOK_PIXEL_ID || '';
    this.hotjarId = import.meta.env.VITE_HOTJAR_ID || '';
    this.init();
  }

  // Initialize all tracking services
  private init(): void {
    this.initGoogleAnalytics();
    this.initFacebookPixel();
    this.initHotjar();
    this.isInitialized = true;
  }

  // Initialize Google Analytics 4
  private initGoogleAnalytics(): void {
    if (!this.gaId) return;

    // Load GA4 script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.gaId}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      window.dataLayer.push(args);
    }
    window.gtag = gtag;

    gtag('js', new Date());
    gtag('config', this.gaId, {
      page_title: 'AffiliateFlow Pro - Renda Recorrente IA',
      page_location: window.location.href,
      custom_map: {
        custom_parameter_1: 'niche',
        custom_parameter_2: 'lead_source'
      }
    });
  }

  // Initialize Facebook Pixel
  private initFacebookPixel(): void {
    if (!this.fbPixelId) return;

    // Facebook Pixel Code
    !function(f: any, b: any, e: any, v: any, n?: any, t?: any, s?: any) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s);
    }(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');

    window.fbq('init', this.fbPixelId);
    window.fbq('track', 'PageView');
  }

  // Initialize Hotjar (free tier: 35 sessions/day)
  private initHotjar(): void {
    if (!this.hotjarId) return;

    (function(h: any, o: any, t: any, j: any, a?: any, r?: any) {
      h.hj = h.hj || function() {
        (h.hj.q = h.hj.q || []).push(arguments);
      };
      h._hjSettings = { hjid: parseInt(this.hotjarId), hjsv: 6 };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script');
      r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
  }

  // Track custom events
  trackEvent(eventData: AnalyticsEvent): void {
    if (!this.isInitialized) return;

    // Google Analytics 4
    if (window.gtag && this.gaId) {
      window.gtag('event', eventData.action, {
        event_category: eventData.category,
        event_label: eventData.label,
        value: eventData.value,
        custom_parameter_1: eventData.customParameters?.niche,
        custom_parameter_2: eventData.customParameters?.source
      });
    }

    // Facebook Pixel
    if (window.fbq && this.fbPixelId) {
      window.fbq('track', eventData.event, {
        content_category: eventData.category,
        content_name: eventData.label,
        value: eventData.value,
        currency: 'BRL'
      });
    }

    // Console log for debugging
    console.log('📊 Analytics Event:', eventData);
  }

  // Track lead capture
  trackLeadCapture(data: {
    email: string;
    source: string;
    niche?: string;
    variant?: string;
  }): void {
    this.trackEvent({
      event: 'Lead',
      category: 'Lead Generation',
      action: 'lead_capture',
      label: data.source,
      value: 1,
      customParameters: {
        niche: data.niche,
        source: data.source,
        variant: data.variant
      }
    });

    // Facebook Pixel Lead event
    if (window.fbq) {
      window.fbq('track', 'Lead', {
        content_name: 'GRIP IA Lead Magnet',
        content_category: data.niche || 'general',
        value: 15.00, // Estimated lead value
        currency: 'BRL'
      });
    }
  }

  // Track conversion (affiliate link click)
  trackConversion(data: ConversionData): void {
    this.trackEvent({
      event: 'Purchase',
      category: 'Conversion',
      action: 'affiliate_click',
      label: data.source,
      value: data.value,
      customParameters: {
        niche: data.niche,
        source: data.source,
        leadId: data.leadId
      }
    });

    // Facebook Pixel Purchase event
    if (window.fbq) {
      window.fbq('track', 'Purchase', {
        value: data.value,
        currency: data.currency,
        content_name: 'GRIP IA Affiliate',
        content_category: data.niche || 'general'
      });
    }
  }

  // Track page views with custom data
  trackPageView(page: string, customData?: Record<string, any>): void {
    if (window.gtag) {
      window.gtag('config', this.gaId, {
        page_title: `AffiliateFlow Pro - ${page}`,
        page_location: window.location.href,
        custom_parameter_1: customData?.niche,
        custom_parameter_2: customData?.source
      });
    }

    if (window.fbq) {
      window.fbq('track', 'PageView');
    }
  }

  // Track email opens (via pixel)
  trackEmailOpen(emailType: string, niche?: string): void {
    this.trackEvent({
      event: 'Email',
      category: 'Email Marketing',
      action: 'email_open',
      label: emailType,
      customParameters: {
        niche: niche,
        emailType: emailType
      }
    });
  }

  // Track button clicks
  trackButtonClick(buttonName: string, location: string, customData?: Record<string, any>): void {
    this.trackEvent({
      event: 'Click',
      category: 'User Interaction',
      action: 'button_click',
      label: `${buttonName} - ${location}`,
      customParameters: customData
    });
  }

  // Track form interactions
  trackFormInteraction(formName: string, action: string, field?: string): void {
    this.trackEvent({
      event: 'Form',
      category: 'Form Interaction',
      action: action,
      label: `${formName}${field ? ` - ${field}` : ''}`,
      customParameters: {
        formName: formName,
        field: field
      }
    });
  }

  // Track scroll depth
  trackScrollDepth(percentage: number): void {
    if (percentage % 25 === 0) { // Track at 25%, 50%, 75%, 100%
      this.trackEvent({
        event: 'Scroll',
        category: 'User Engagement',
        action: 'scroll_depth',
        label: `${percentage}%`,
        value: percentage
      });
    }
  }

  // Track time on page
  trackTimeOnPage(seconds: number): void {
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0 && minutes % 1 === 0) { // Track every minute
      this.trackEvent({
        event: 'Engagement',
        category: 'User Engagement',
        action: 'time_on_page',
        label: `${minutes} minutes`,
        value: seconds
      });
    }
  }

  // Get UTM parameters
  getUTMParameters(): Record<string, string> {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      utm_source: urlParams.get('utm_source') || '',
      utm_medium: urlParams.get('utm_medium') || '',
      utm_campaign: urlParams.get('utm_campaign') || '',
      utm_term: urlParams.get('utm_term') || '',
      utm_content: urlParams.get('utm_content') || ''
    };
  }
}

// Global analytics instance
export const analytics = new AnalyticsService();

// Declare global types for TypeScript
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
    fbq: (...args: any[]) => void;
    hj: (...args: any[]) => void;
    _hjSettings: { hjid: number; hjsv: number };
  }
}
