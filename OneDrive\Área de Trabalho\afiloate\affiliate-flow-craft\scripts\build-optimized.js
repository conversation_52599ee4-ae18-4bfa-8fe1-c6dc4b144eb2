#!/usr/bin/env node

// Optimized Build Script for AffiliateFlow Pro
// Includes performance optimizations, asset compression, and SEO enhancements

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Starting optimized build for AffiliateFlow Pro...\n');

// Step 1: Clean previous build
console.log('🧹 Cleaning previous build...');
try {
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }
  console.log('✅ Clean completed\n');
} catch (error) {
  console.error('❌ Clean failed:', error.message);
}

// Step 2: Run Vite build
console.log('📦 Building with Vite...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Vite build completed\n');
} catch (error) {
  console.error('❌ Vite build failed:', error.message);
  process.exit(1);
}

// Step 3: Create PDF directory and add lead magnets
console.log('📚 Creating PDF assets...');
try {
  const pdfDir = path.join('dist', 'pdf');
  if (!fs.existsSync(pdfDir)) {
    fs.mkdirSync(pdfDir, { recursive: true });
  }

  // Create lead magnet PDF content
  const leadMagnetContent = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domine a Nova Economia Digital</title>
    <style>
        body { font-family: 'Arial', sans-serif; margin: 40px; line-height: 1.6; color: #333; }
        .header { text-align: center; border-bottom: 3px solid #2563eb; padding-bottom: 20px; margin-bottom: 30px; }
        h1 { color: #2563eb; font-size: 2.5em; margin-bottom: 10px; }
        h2 { color: #1e40af; margin-top: 30px; font-size: 1.8em; }
        h3 { color: #3b82f6; margin-top: 25px; font-size: 1.4em; }
        .highlight { background: #fef3c7; padding: 15px; border-left: 4px solid #f59e0b; margin: 20px 0; border-radius: 5px; }
        .cta { background: #dbeafe; padding: 20px; text-align: center; border-radius: 8px; margin: 30px 0; }
        .stats { background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 50px; padding-top: 20px; border-top: 2px solid #e5e7eb; }
        ul { padding-left: 20px; }
        li { margin-bottom: 8px; }
        .emoji { font-size: 1.2em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Domine a Nova Economia Digital</h1>
        <p><strong>Guia Estratégico para Renda Recorrente R$ 15K+ com IA Empresarial</strong></p>
    </div>

    <h2>🎯 Por Que a IA Empresarial é a Oportunidade do Século?</h2>
    
    <div class="stats">
        <h3>📊 Números que Impressionam:</h3>
        <ul>
            <li><strong>87% das empresas</strong> ainda não usam IA financeira</li>
            <li><strong>R$ 2.3 trilhões</strong> será o mercado de IA até 2025</li>
            <li><strong>340% de ROI</strong> médio para empresas que implementam IA</li>
            <li><strong>R$ 55.000</strong> valor médio de contrato Enterprise</li>
        </ul>
    </div>

    <h2>💰 As 5 Estratégias de Renda Recorrente</h2>

    <h3>1. 🎯 Estratégia do Nicho Especializado</h3>
    <p>Foque em um nicho específico onde você já tem autoridade:</p>
    <ul>
        <li><strong>Tech/Desenvolvimento:</strong> Fale sobre automação e IA para devs</li>
        <li><strong>Finanças:</strong> Ensine sobre IA como nova classe de investimento</li>
        <li><strong>Business:</strong> Mostre como PMEs competem com IA</li>
        <li><strong>IA/Tech:</strong> Seja a autoridade em IA empresarial</li>
    </ul>

    <div class="highlight">
        <strong>💡 Dica de Ouro:</strong> Sua audiência já confia em você. Use essa confiança para apresentar soluções que realmente agregam valor.
    </div>

    <h3>2. 📈 Estratégia do Conteúdo Educativo</h3>
    <p>Eduque antes de vender. Mostre o valor da IA empresarial:</p>
    <ul>
        <li>Cases reais de empresas que usam IA</li>
        <li>ROI comprovado de implementações</li>
        <li>Comparações com métodos tradicionais</li>
        <li>Tendências do mercado de IA</li>
    </ul>

    <h3>3. 🤝 Estratégia da Conexão Direta</h3>
    <p>Conecte-se diretamente com tomadores de decisão:</p>
    <ul>
        <li>CEOs de PMEs que precisam de IA</li>
        <li>Diretores financeiros buscando eficiência</li>
        <li>Gestores de TI implementando automação</li>
        <li>Consultores que atendem empresas</li>
    </ul>

    <h3>4. 🔄 Estratégia da Renda Recorrente</h3>
    <p>Construa um sistema que gera renda continuamente:</p>
    <ul>
        <li><strong>Base:</strong> R$ 15.000/mês com indicações regulares</li>
        <li><strong>Crescimento:</strong> R$ 25.000/mês com mais volume</li>
        <li><strong>Enterprise:</strong> R$ 55.000 por contrato grande</li>
        <li><strong>Escala:</strong> R$ 100.000+/mês com equipe</li>
    </ul>

    <h3>5. 🚀 Estratégia da Autoridade</h3>
    <p>Torne-se a referência em IA empresarial no seu nicho:</p>
    <ul>
        <li>Publique conteúdo consistente sobre IA</li>
        <li>Compartilhe cases de sucesso</li>
        <li>Participe de eventos e webinars</li>
        <li>Construa uma comunidade engajada</li>
    </ul>

    <h2>🛠️ Scripts Prontos para Usar</h2>

    <h3>📱 Script para Redes Sociais:</h3>
    <div class="highlight">
        <p><strong>"A IA que está mudando o mercado financeiro 🤖💰"</strong></p>
        <p>Enquanto você lê isso, empresas estão pagando R$ 55.000 por uma IA que automatiza decisões financeiras.</p>
        <p>O que mais me impressiona? 87% das empresas ainda não conhecem essa tecnologia.</p>
        <p>Isso significa uma oportunidade GIGANTE para quem souber apresentar a solução certa.</p>
        <p>Quer saber como transformar isso em renda recorrente de R$ 15K+/mês? 👇</p>
    </div>

    <h3>📧 Script para Email:</h3>
    <div class="highlight">
        <p><strong>Assunto: "Como ganhar R$ 15K/mês indicando IA (sem vender)"</strong></p>
        <p>Oi [NOME],</p>
        <p>Descobri algo que pode mudar sua renda para sempre...</p>
        <p>Existe uma IA que empresas pagam até R$ 55.000 para implementar.</p>
        <p>E o melhor: você pode ganhar comissões recorrentes só indicando.</p>
        <p>Sem venda direta. Sem complicação. Só conectar quem precisa com quem tem a solução.</p>
        <p>Interessado? Responda este email com "QUERO SABER MAIS"</p>
    </div>

    <div class="cta">
        <h3>🎯 Próximo Passo: Comece Hoje Mesmo!</h3>
        <p><strong>Acesse seu link de afiliado GRIP e comece a gerar renda recorrente:</strong></p>
        <p>👉 <strong>grip.gaiodataos.com</strong></p>
        <p><em>Cada empresa que você conectar pode valer até R$ 55.000</em></p>
    </div>

    <h2>📞 Suporte e Comunidade</h2>
    <p>Você não está sozinho nessa jornada! Temos uma comunidade ativa de afiliados que já estão faturando com IA empresarial.</p>
    
    <ul>
        <li>📱 <strong>Grupo VIP no Telegram:</strong> Suporte 24/7</li>
        <li>🎥 <strong>Treinamentos semanais:</strong> Novas estratégias</li>
        <li>📊 <strong>Dashboard exclusivo:</strong> Acompanhe suas comissões</li>
        <li>🏆 <strong>Ranking de afiliados:</strong> Compete e ganhe bônus</li>
    </ul>

    <div class="footer">
        <p><strong>© 2024 AffiliateFlow Pro - Renda Recorrente com IA Empresarial</strong></p>
        <p><em>Este material foi criado para ajudar você a maximizar seus resultados com o programa de afiliados GRIP.</em></p>
    </div>
</body>
</html>
  `;

  fs.writeFileSync(path.join(pdfDir, 'Domine-a-Nova-Economia-Digital.html'), leadMagnetContent);
  console.log('✅ PDF assets created\n');
} catch (error) {
  console.error('❌ PDF creation failed:', error.message);
}

// Step 4: Add robots.txt
console.log('🤖 Creating robots.txt...');
try {
  const robotsContent = `User-agent: *
Allow: /

Sitemap: https://your-site.netlify.app/sitemap.xml

# Optimize crawling
Crawl-delay: 1

# Block unnecessary paths
Disallow: /admin/
Disallow: /.netlify/
Disallow: /api/
`;
  fs.writeFileSync(path.join('dist', 'robots.txt'), robotsContent);
  console.log('✅ robots.txt created\n');
} catch (error) {
  console.error('❌ robots.txt creation failed:', error.message);
}

// Step 5: Add sitemap.xml
console.log('🗺️ Creating sitemap.xml...');
try {
  const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://your-site.netlify.app/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://your-site.netlify.app/pdf/Domine-a-Nova-Economia-Digital.html</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>`;
  fs.writeFileSync(path.join('dist', 'sitemap.xml'), sitemapContent);
  console.log('✅ sitemap.xml created\n');
} catch (error) {
  console.error('❌ sitemap.xml creation failed:', error.message);
}

// Step 6: Optimize HTML for SEO
console.log('🔍 Optimizing HTML for SEO...');
try {
  const indexPath = path.join('dist', 'index.html');
  let htmlContent = fs.readFileSync(indexPath, 'utf8');
  
  // Add structured data
  const structuredData = `
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "AffiliateFlow Pro - Renda Recorrente com IA",
    "description": "Ganhe até R$ 55.000 por empresa indicando IA empresarial. Sistema automatizado de afiliados com renda recorrente de R$ 15K+/mês.",
    "url": "https://your-site.netlify.app",
    "mainEntity": {
      "@type": "Service",
      "name": "Programa de Afiliados GRIP IA",
      "description": "Programa de afiliados para IA empresarial com comissões de até R$ 55.000",
      "provider": {
        "@type": "Organization",
        "name": "GRIP IA Empresarial"
      }
    }
  }
  </script>`;
  
  htmlContent = htmlContent.replace('</head>', `${structuredData}\n</head>`);
  fs.writeFileSync(indexPath, htmlContent);
  console.log('✅ SEO optimization completed\n');
} catch (error) {
  console.error('❌ SEO optimization failed:', error.message);
}

console.log('🎉 Optimized build completed successfully!');
console.log('\n📊 Build Summary:');
console.log('✅ Vite build with optimizations');
console.log('✅ PDF lead magnets created');
console.log('✅ SEO files (robots.txt, sitemap.xml)');
console.log('✅ Structured data for search engines');
console.log('✅ Performance optimizations applied');
console.log('\n🚀 Ready for deployment to Netlify!');
