// Free Integrations Service
// Integrates with free tiers of popular services for maximum functionality at zero cost

interface IntegrationConfig {
  name: string;
  isEnabled: boolean;
  apiKey?: string;
  endpoint?: string;
  maxRequests?: number;
  currentRequests?: number;
}

interface WebhookPayload {
  event: string;
  data: Record<string, any>;
  timestamp: Date;
  source: string;
}

class IntegrationsService {
  private integrations: Map<string, IntegrationConfig> = new Map();
  private webhookQueue: WebhookPayload[] = [];
  private isProcessing: boolean = false;

  constructor() {
    this.initializeIntegrations();
  }

  // Initialize all free integrations
  private initializeIntegrations(): void {
    // Airtable (Free: 1,200 records/base, 5 requests/second)
    this.integrations.set('airtable', {
      name: 'Airtable',
      isEnabled: true,
      apiKey: import.meta.env.VITE_AIRTABLE_API_KEY || '',
      endpoint: import.meta.env.VITE_AIRTABLE_BASE_URL || '',
      maxRequests: 5000, // per month on free tier
      currentRequests: 0
    });

    // EmailJS (Free: 200 emails/month)
    this.integrations.set('emailjs', {
      name: '<PERSON>ailJ<PERSON>',
      isEnabled: true,
      apiKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY || '',
      maxRequests: 200,
      currentRequests: 0
    });

    // Netlify Functions (Free: 125,000 requests/month)
    this.integrations.set('netlify', {
      name: 'Netlify Functions',
      isEnabled: true,
      endpoint: '/.netlify/functions/',
      maxRequests: 125000,
      currentRequests: 0
    });

    // GitHub (Free: Unlimited public repos, 500MB storage)
    this.integrations.set('github', {
      name: 'GitHub Storage',
      isEnabled: true,
      apiKey: import.meta.env.VITE_GITHUB_TOKEN || '',
      endpoint: 'https://api.github.com/repos/',
      maxRequests: 5000, // per hour
      currentRequests: 0
    });

    // Cloudinary (Free: 25 credits/month, 25GB storage)
    this.integrations.set('cloudinary', {
      name: 'Cloudinary',
      isEnabled: true,
      apiKey: import.meta.env.VITE_CLOUDINARY_API_KEY || '',
      endpoint: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME || '',
      maxRequests: 25000,
      currentRequests: 0
    });

    // Zapier (Free: 100 tasks/month)
    this.integrations.set('zapier', {
      name: 'Zapier Webhooks',
      isEnabled: true,
      endpoint: import.meta.env.VITE_ZAPIER_WEBHOOK_URL || '',
      maxRequests: 100,
      currentRequests: 0
    });

    // Make.com (Free: 1,000 operations/month)
    this.integrations.set('make', {
      name: 'Make.com',
      isEnabled: true,
      endpoint: import.meta.env.VITE_MAKE_WEBHOOK_URL || '',
      maxRequests: 1000,
      currentRequests: 0
    });

    // Typeform (Free: 100 responses/month)
    this.integrations.set('typeform', {
      name: 'Typeform',
      isEnabled: true,
      apiKey: import.meta.env.VITE_TYPEFORM_API_KEY || '',
      maxRequests: 100,
      currentRequests: 0
    });
  }

  // Send data to Airtable
  async sendToAirtable(tableName: string, data: Record<string, any>): Promise<boolean> {
    const config = this.integrations.get('airtable');
    if (!config?.isEnabled || !config.apiKey) return false;

    try {
      const response = await fetch(`${config.endpoint}/${tableName}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fields: data
        })
      });

      if (response.ok) {
        config.currentRequests = (config.currentRequests || 0) + 1;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Airtable error:', error);
      return false;
    }
  }

  // Upload file to GitHub (as free CDN)
  async uploadToGitHub(fileName: string, content: string, folder: string = 'assets'): Promise<string | null> {
    const config = this.integrations.get('github');
    if (!config?.isEnabled || !config.apiKey) return null;

    try {
      const repo = import.meta.env.VITE_GITHUB_REPO || 'affiliate-assets';
      const path = `${folder}/${fileName}`;
      
      const response = await fetch(`${config.endpoint}${repo}/contents/${path}`, {
        method: 'PUT',
        headers: {
          'Authorization': `token ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: `Upload ${fileName}`,
          content: btoa(content), // Base64 encode
          branch: 'main'
        })
      });

      if (response.ok) {
        const result = await response.json();
        config.currentRequests = (config.currentRequests || 0) + 1;
        return result.content.download_url;
      }
      return null;
    } catch (error) {
      console.error('GitHub upload error:', error);
      return null;
    }
  }

  // Upload image to Cloudinary
  async uploadToCloudinary(file: File, folder: string = 'affiliate'): Promise<string | null> {
    const config = this.integrations.get('cloudinary');
    if (!config?.isEnabled || !config.endpoint) return null;

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET || 'affiliate_preset');
      formData.append('folder', folder);

      const response = await fetch(`https://api.cloudinary.com/v1_1/${config.endpoint}/image/upload`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        config.currentRequests = (config.currentRequests || 0) + 1;
        return result.secure_url;
      }
      return null;
    } catch (error) {
      console.error('Cloudinary upload error:', error);
      return null;
    }
  }

  // Send webhook to automation platforms
  async sendWebhook(platform: 'zapier' | 'make', data: Record<string, any>): Promise<boolean> {
    const config = this.integrations.get(platform);
    if (!config?.isEnabled || !config.endpoint) return false;

    try {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        config.currentRequests = (config.currentRequests || 0) + 1;
        return true;
      }
      return false;
    } catch (error) {
      console.error(`${platform} webhook error:`, error);
      return false;
    }
  }

  // Queue webhook for batch processing
  queueWebhook(payload: WebhookPayload): void {
    this.webhookQueue.push(payload);
    this.processWebhookQueue();
  }

  // Process webhook queue to avoid rate limits
  private async processWebhookQueue(): Promise<void> {
    if (this.isProcessing || this.webhookQueue.length === 0) return;

    this.isProcessing = true;

    while (this.webhookQueue.length > 0) {
      const payload = this.webhookQueue.shift()!;
      
      // Send to both Zapier and Make.com for redundancy
      await Promise.all([
        this.sendWebhook('zapier', payload),
        this.sendWebhook('make', payload)
      ]);

      // Rate limiting: wait 1 second between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.isProcessing = false;
  }

  // Create PDF and upload to GitHub
  async createAndUploadPDF(content: string, fileName: string): Promise<string | null> {
    try {
      // Simple HTML to PDF conversion (for basic PDFs)
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${fileName}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            h1 { color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px; }
            h2 { color: #1e40af; margin-top: 30px; }
            .highlight { background: #fef3c7; padding: 15px; border-left: 4px solid #f59e0b; margin: 20px 0; }
            .cta { background: #dbeafe; padding: 20px; text-align: center; border-radius: 8px; margin: 30px 0; }
          </style>
        </head>
        <body>
          ${content}
        </body>
        </html>
      `;

      // Upload HTML version (can be converted to PDF client-side)
      const url = await this.uploadToGitHub(`${fileName}.html`, htmlContent, 'pdf');
      return url;
    } catch (error) {
      console.error('PDF creation error:', error);
      return null;
    }
  }

  // Sync lead data across all platforms
  async syncLeadData(leadData: Record<string, any>): Promise<void> {
    const promises = [];

    // Send to Airtable
    promises.push(this.sendToAirtable('Leads', leadData));

    // Queue webhooks for automation
    this.queueWebhook({
      event: 'lead_captured',
      data: leadData,
      timestamp: new Date(),
      source: 'affiliate_flow'
    });

    // Send to Netlify function for additional processing
    promises.push(this.callNetlifyFunction('process-lead', leadData));

    await Promise.allSettled(promises);
  }

  // Call Netlify function
  async callNetlifyFunction(functionName: string, data: Record<string, any>): Promise<any> {
    const config = this.integrations.get('netlify');
    if (!config?.isEnabled) return null;

    try {
      const response = await fetch(`${config.endpoint}${functionName}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        config.currentRequests = (config.currentRequests || 0) + 1;
        return await response.json();
      }
      return null;
    } catch (error) {
      console.error('Netlify function error:', error);
      return null;
    }
  }

  // Get integration status
  getIntegrationStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    this.integrations.forEach((config, name) => {
      status[name] = {
        enabled: config.isEnabled,
        usage: config.currentRequests || 0,
        limit: config.maxRequests || 0,
        usagePercentage: config.maxRequests ? 
          Math.round(((config.currentRequests || 0) / config.maxRequests) * 100) : 0
      };
    });

    return status;
  }

  // Reset monthly counters (call this monthly)
  resetMonthlyCounters(): void {
    this.integrations.forEach(config => {
      config.currentRequests = 0;
    });
  }

  // Check if integration is available
  isIntegrationAvailable(name: string): boolean {
    const config = this.integrations.get(name);
    if (!config?.isEnabled) return false;
    
    if (config.maxRequests && config.currentRequests) {
      return config.currentRequests < config.maxRequests;
    }
    
    return true;
  }
}

// Global integrations service
export const integrations = new IntegrationsService();

// Helper functions for common operations
export const uploadAsset = async (file: File, type: 'image' | 'document' = 'image'): Promise<string | null> => {
  if (type === 'image') {
    return await integrations.uploadToCloudinary(file);
  } else {
    // Convert file to base64 and upload to GitHub
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = async () => {
        const content = reader.result as string;
        const url = await integrations.uploadToGitHub(file.name, content, 'documents');
        resolve(url);
      };
      reader.readAsDataURL(file);
    });
  }
};

export const createLeadMagnet = async (title: string, content: string): Promise<string | null> => {
  return await integrations.createAndUploadPDF(content, title.replace(/\s+/g, '-').toLowerCase());
};
