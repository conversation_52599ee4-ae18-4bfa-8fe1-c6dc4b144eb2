// Teste rápido da API do Brevo
const API_KEY = '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';

async function testBrevoConnection() {
  try {
    console.log('🔍 Testando conexão com Brevo...');
    
    const response = await fetch('https://api.brevo.com/v3/account', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'api-key': API_KEY
      }
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Conexão com Brevo SUCESSO!');
      console.log('📧 Plano:', data.plan?.type || 'Free');
      console.log('📊 Emails restantes hoje:', data.plan?.creditsRemaining || 'Ilimitado');
      console.log('👤 Email da conta:', data.email);
      
      // Testar criação de contato
      await testCreateContact();
      
    } else {
      console.error('❌ Erro na conexão:', data);
    }
  } catch (error) {
    console.error('❌ Erro de rede:', error.message);
  }
}

async function testCreateContact() {
  try {
    console.log('\n🔍 Testando criação de contato...');
    
    const testContact = {
      email: '<EMAIL>',
      attributes: {
        FIRSTNAME: 'Teste',
        LASTNAME: 'AffiliateFlow',
        NICHE: 'TECH',
        SOURCE: 'test'
      },
      listIds: [1] // Lista padrão
    };

    const response = await fetch('https://api.brevo.com/v3/contacts', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'api-key': API_KEY
      },
      body: JSON.stringify(testContact)
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Contato criado com sucesso!');
      console.log('🆔 ID do contato:', data.id);
    } else if (response.status === 400 && data.code === 'duplicate_parameter') {
      console.log('✅ Contato já existe (isso é normal)');
    } else {
      console.log('⚠️ Resposta da API:', data);
    }
  } catch (error) {
    console.error('❌ Erro ao criar contato:', error.message);
  }
}

// Executar teste
testBrevoConnection();
