// ===================================================================
// ENTERPRISE LOGGING SYSTEM - AffiliateFlow Pro
// Structured logging with levels, contexts, and performance tracking
// ===================================================================

import { config } from '../config';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4
}

export interface ILogEntry {
  readonly timestamp: Date;
  readonly level: LogLevel;
  readonly message: string;
  readonly context?: string;
  readonly data?: Record<string, unknown>;
  readonly error?: Error;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly requestId?: string;
}

export interface IPerformanceMetric {
  readonly name: string;
  readonly startTime: number;
  readonly endTime?: number;
  readonly duration?: number;
  readonly metadata?: Record<string, unknown>;
}

class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;
  private sessionId: string;
  private performanceMetrics: Map<string, IPerformanceMetric> = new Map();

  private constructor() {
    this.logLevel = config.isProduction() ? LogLevel.INFO : LogLevel.DEBUG;
    this.sessionId = this.generateSessionId();
    
    if (config.isDebugMode()) {
      console.log('🔍 Logger initialized in debug mode');
    }
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel;
  }

  private formatMessage(entry: ILogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const level = LogLevel[entry.level];
    const context = entry.context ? `[${entry.context}]` : '';
    
    return `${timestamp} ${level} ${context} ${entry.message}`;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: string,
    data?: Record<string, unknown>,
    error?: Error
  ): ILogEntry {
    return {
      timestamp: new Date(),
      level,
      message,
      context,
      data,
      error,
      sessionId: this.sessionId,
      requestId: this.generateRequestId()
    };
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private outputLog(entry: ILogEntry): void {
    if (!this.shouldLog(entry.level)) return;

    const formattedMessage = this.formatMessage(entry);
    
    switch (entry.level) {
      case LogLevel.ERROR:
        console.error(formattedMessage, entry.data, entry.error);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, entry.data);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, entry.data);
        break;
      case LogLevel.DEBUG:
        console.debug(formattedMessage, entry.data);
        break;
      case LogLevel.TRACE:
        console.trace(formattedMessage, entry.data);
        break;
    }

    // Send to external logging service in production
    if (config.isProduction()) {
      this.sendToExternalService(entry);
    }
  }

  private async sendToExternalService(entry: ILogEntry): Promise<void> {
    try {
      // In production, send to logging service like LogRocket, Sentry, etc.
      // For now, we'll just store in localStorage for demo
      const logs = JSON.parse(localStorage.getItem('app_logs') || '[]');
      logs.push(entry);
      
      // Keep only last 100 logs
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }
      
      localStorage.setItem('app_logs', JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to send log to external service:', error);
    }
  }

  // Public logging methods
  public error(message: string, context?: string, data?: Record<string, unknown>, error?: Error): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, data, error);
    this.outputLog(entry);
  }

  public warn(message: string, context?: string, data?: Record<string, unknown>): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context, data);
    this.outputLog(entry);
  }

  public info(message: string, context?: string, data?: Record<string, unknown>): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context, data);
    this.outputLog(entry);
  }

  public debug(message: string, context?: string, data?: Record<string, unknown>): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context, data);
    this.outputLog(entry);
  }

  public trace(message: string, context?: string, data?: Record<string, unknown>): void {
    const entry = this.createLogEntry(LogLevel.TRACE, message, context, data);
    this.outputLog(entry);
  }

  // Performance tracking
  public startPerformanceTimer(name: string, metadata?: Record<string, unknown>): void {
    const metric: IPerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata
    };
    
    this.performanceMetrics.set(name, metric);
    this.debug(`Performance timer started: ${name}`, 'PERFORMANCE', metadata);
  }

  public endPerformanceTimer(name: string): number | null {
    const metric = this.performanceMetrics.get(name);
    if (!metric) {
      this.warn(`Performance timer not found: ${name}`, 'PERFORMANCE');
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    
    const completedMetric: IPerformanceMetric = {
      ...metric,
      endTime,
      duration
    };

    this.performanceMetrics.set(name, completedMetric);
    
    this.info(
      `Performance timer completed: ${name} (${duration.toFixed(2)}ms)`,
      'PERFORMANCE',
      { duration, ...metric.metadata }
    );

    return duration;
  }

  public getPerformanceMetrics(): IPerformanceMetric[] {
    return Array.from(this.performanceMetrics.values());
  }

  // Context-specific loggers
  public createContextLogger(context: string) {
    return {
      error: (message: string, data?: Record<string, unknown>, error?: Error) => 
        this.error(message, context, data, error),
      warn: (message: string, data?: Record<string, unknown>) => 
        this.warn(message, context, data),
      info: (message: string, data?: Record<string, unknown>) => 
        this.info(message, context, data),
      debug: (message: string, data?: Record<string, unknown>) => 
        this.debug(message, context, data),
      trace: (message: string, data?: Record<string, unknown>) => 
        this.trace(message, context, data)
    };
  }

  // Utility methods
  public setLogLevel(level: LogLevel): void {
    this.logLevel = level;
    this.info(`Log level changed to: ${LogLevel[level]}`, 'LOGGER');
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public getLogs(): ILogEntry[] {
    try {
      return JSON.parse(localStorage.getItem('app_logs') || '[]');
    } catch {
      return [];
    }
  }

  public clearLogs(): void {
    localStorage.removeItem('app_logs');
    this.info('Logs cleared', 'LOGGER');
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Export convenience functions
export const createLogger = (context: string) => logger.createContextLogger(context);

// Performance decorator
export function logPerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const timerName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      logger.startPerformanceTimer(timerName, { 
        method: propertyKey, 
        class: target.constructor.name 
      });
      
      try {
        const result = await originalMethod.apply(this, args);
        logger.endPerformanceTimer(timerName);
        return result;
      } catch (error) {
        logger.endPerformanceTimer(timerName);
        logger.error(
          `Method ${timerName} failed`,
          'PERFORMANCE',
          { args: args.length },
          error as Error
        );
        throw error;
      }
    };

    return descriptor;
  };
}

// Error boundary logger
export const logError = (error: Error, context: string, additionalData?: Record<string, unknown>) => {
  logger.error(
    `Unhandled error in ${context}: ${error.message}`,
    'ERROR_BOUNDARY',
    {
      stack: error.stack,
      name: error.name,
      ...additionalData
    },
    error
  );
};
