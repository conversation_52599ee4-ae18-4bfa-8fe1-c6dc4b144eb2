// ===================================================================
// CORE TYPES - AffiliateFlow Pro
// Enterprise-grade type definitions with strict typing
// ===================================================================

export interface IUser {
  readonly id: string;
  readonly name: string;
  readonly email: string;
  readonly phone?: string;
  readonly niche: UserNiche;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly metadata?: Record<string, unknown>;
}

export type UserNiche = 'tech' | 'finance' | 'business' | 'ai' | 'general';

export interface ILead extends IUser {
  readonly source: LeadSource;
  readonly status: LeadStatus;
  readonly affiliateLink: string;
  readonly conversionData?: IConversionData;
  readonly emailSequence?: IEmailSequenceStatus;
}

export type LeadSource = 
  | 'hero-section' 
  | 'step-form' 
  | 'exit-intent' 
  | 'social-media' 
  | 'referral';

export type LeadStatus = 
  | 'captured' 
  | 'qualified' 
  | 'nurturing' 
  | 'converted' 
  | 'churned';

export interface IConversionData {
  readonly stepCompleted: number;
  readonly timeToConversion: number;
  readonly touchpoints: ITouchpoint[];
  readonly revenue?: number;
}

export interface ITouchpoint {
  readonly timestamp: Date;
  readonly type: TouchpointType;
  readonly data: Record<string, unknown>;
}

export type TouchpointType = 
  | 'page_view' 
  | 'form_submit' 
  | 'email_open' 
  | 'email_click' 
  | 'app_download';

export interface IEmailSequenceStatus {
  readonly currentStep: number;
  readonly totalSteps: number;
  readonly lastEmailSent: Date;
  readonly nextEmailScheduled?: Date;
  readonly openRate: number;
  readonly clickRate: number;
}

export interface IEmailTemplate {
  readonly id: string;
  readonly name: string;
  readonly subject: string;
  readonly content: string;
  readonly niche: UserNiche;
  readonly type: EmailType;
  readonly variables: string[];
  readonly aiGenerated: boolean;
}

export type EmailType = 
  | 'welcome' 
  | 'nurture' 
  | 'conversion' 
  | 'reactivation' 
  | 'upsell';

export interface IAnalyticsEvent {
  readonly eventId: string;
  readonly userId?: string;
  readonly sessionId: string;
  readonly timestamp: Date;
  readonly eventType: AnalyticsEventType;
  readonly properties: Record<string, unknown>;
  readonly source: string;
}

export type AnalyticsEventType = 
  | 'page_view'
  | 'form_submit'
  | 'button_click'
  | 'email_sent'
  | 'email_opened'
  | 'email_clicked'
  | 'conversion'
  | 'error';

export interface IApiResponse<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: IApiError;
  readonly timestamp: Date;
  readonly requestId: string;
}

export interface IApiError {
  readonly code: string;
  readonly message: string;
  readonly details?: Record<string, unknown>;
}

export interface IConfiguration {
  readonly brevo: IBrevoConfig;
  readonly analytics: IAnalyticsConfig;
  readonly grip: IGripConfig;
  readonly app: IAppConfig;
}

export interface IBrevoConfig {
  readonly apiKey: string;
  readonly listId: string;
  readonly templateIds: Record<EmailType, string>;
  readonly webhookUrl?: string;
}

export interface IAnalyticsConfig {
  readonly googleAnalyticsId?: string;
  readonly facebookPixelId?: string;
  readonly hotjarId?: string;
  readonly enableDebug: boolean;
}

export interface IGripConfig {
  readonly affiliateBaseUrl: string;
  readonly affiliateId: string;
  readonly androidAppUrl: string;
  readonly iosAppUrl: string;
}

export interface IAppConfig {
  readonly environment: 'development' | 'staging' | 'production';
  readonly apiBaseUrl: string;
  readonly enableAI: boolean;
  readonly enableAnalytics: boolean;
  readonly debugMode: boolean;
}

// State Management Types
export interface IAppState {
  readonly user: IUser | null;
  readonly currentStep: number;
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly analytics: IAnalyticsState;
  readonly emailMarketing: IEmailMarketingState;
}

export interface IAnalyticsState {
  readonly sessionId: string;
  readonly events: IAnalyticsEvent[];
  readonly isTracking: boolean;
}

export interface IEmailMarketingState {
  readonly isConnected: boolean;
  readonly lastEmailSent?: Date;
  readonly sequenceStatus?: IEmailSequenceStatus;
  readonly templates: IEmailTemplate[];
}

// Action Types for State Management
export type AppAction = 
  | { type: 'SET_USER'; payload: IUser }
  | { type: 'SET_STEP'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'TRACK_EVENT'; payload: IAnalyticsEvent }
  | { type: 'EMAIL_SENT'; payload: { templateId: string; userId: string } };

// Utility Types
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Validation Types
export interface IValidationResult {
  readonly isValid: boolean;
  readonly errors: IValidationError[];
}

export interface IValidationError {
  readonly field: string;
  readonly message: string;
  readonly code: string;
}
