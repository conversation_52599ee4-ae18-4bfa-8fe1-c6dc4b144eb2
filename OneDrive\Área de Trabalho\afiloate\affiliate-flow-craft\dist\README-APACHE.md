# 🚀 AffiliateFlow - Deploy no Apache

## 📁 Estrutura dos Arquivos

Esta pasta `dist` contém todos os arquivos necessários para fazer deploy da landing page AffiliateFlow em um servidor Apache.

```
dist/
├── .htaccess          # Configurações do Apache
├── index.html         # Landing page principal
└── README-APACHE.md   # Este arquivo
```

## 🔧 Configuração do Apache

### 1. **Requisitos do Servidor**
- Apache 2.4+
- Módulos habilitados:
  - `mod_rewrite` (para URLs amigáveis)
  - `mod_headers` (para headers de segurança)
  - `mod_deflate` (para compressão)
  - `mod_expires` (para cache)

### 2. **Verificar Módulos Habilitados**
```bash
# No Ubuntu/Debian
sudo a2enmod rewrite headers deflate expires
sudo systemctl restart apache2

# No CentOS/RHEL
# Verificar se os módulos estão no httpd.conf
```

### 3. **Configuração do Virtual Host**
```apache
<VirtualHost *:80>
    ServerName seudominio.com
    ServerAlias www.seudominio.com
    DocumentRoot /var/www/html/affiliateflow
    
    # Permitir .htaccess
    <Directory /var/www/html/affiliateflow>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Logs
    ErrorLog ${APACHE_LOG_DIR}/affiliateflow_error.log
    CustomLog ${APACHE_LOG_DIR}/affiliateflow_access.log combined
</VirtualHost>

# Para HTTPS (recomendado)
<VirtualHost *:443>
    ServerName seudominio.com
    ServerAlias www.seudominio.com
    DocumentRoot /var/www/html/affiliateflow
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    <Directory /var/www/html/affiliateflow>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/affiliateflow_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/affiliateflow_ssl_access.log combined
</VirtualHost>
```

## 📤 Instruções de Deploy

### **Opção 1: Upload via FTP/SFTP**
1. Conecte-se ao seu servidor via FTP/SFTP
2. Navegue até a pasta do seu domínio (ex: `/public_html/` ou `/var/www/html/`)
3. Faça upload de todos os arquivos da pasta `dist`
4. Certifique-se que o arquivo `.htaccess` foi enviado (pode estar oculto)

### **Opção 2: Upload via cPanel**
1. Acesse o cPanel do seu hosting
2. Vá em "Gerenciador de Arquivos"
3. Navegue até `public_html`
4. Faça upload dos arquivos da pasta `dist`
5. Extraia se necessário

### **Opção 3: Linha de Comando (SSH)**
```bash
# Copiar arquivos para o servidor
scp -r dist/* usuario@servidor:/var/www/html/affiliateflow/

# Ou usando rsync
rsync -avz dist/ usuario@servidor:/var/www/html/affiliateflow/

# Definir permissões corretas
sudo chown -R www-data:www-data /var/www/html/affiliateflow
sudo chmod -R 755 /var/www/html/affiliateflow
```

## 🔒 Configurações de Segurança

O arquivo `.htaccess` já inclui:
- ✅ Redirecionamento forçado para HTTPS
- ✅ Headers de segurança (XSS, Clickjacking, etc.)
- ✅ Proteção contra acesso a arquivos sensíveis
- ✅ Desabilitação de listagem de diretórios

## ⚡ Otimizações de Performance

Configurações já incluídas:
- ✅ Compressão GZIP para todos os arquivos
- ✅ Cache de navegador otimizado
- ✅ Headers de cache apropriados
- ✅ Keep-Alive habilitado

## 🧪 Testando o Deploy

### 1. **Verificar se o site está funcionando**
```bash
curl -I https://seudominio.com
```

### 2. **Testar compressão GZIP**
```bash
curl -H "Accept-Encoding: gzip" -I https://seudominio.com
```

### 3. **Verificar headers de segurança**
```bash
curl -I https://seudominio.com | grep -E "(X-Frame-Options|X-XSS-Protection|X-Content-Type-Options)"
```

### 4. **Testar formulário**
- Acesse o site
- Preencha o formulário de captura de leads
- Verifique se não há erros no console do navegador

## 🔧 Troubleshooting

### **Erro 500 - Internal Server Error**
- Verifique se os módulos do Apache estão habilitados
- Confira as permissões dos arquivos (755 para pastas, 644 para arquivos)
- Verifique os logs de erro: `tail -f /var/log/apache2/error.log`

### **Arquivo .htaccess não funciona**
- Certifique-se que `AllowOverride All` está configurado no Virtual Host
- Verifique se o módulo `mod_rewrite` está habilitado

### **CSS/JS não carregam**
- Verifique se os arquivos estão no local correto
- Confira as permissões dos arquivos
- Teste sem cache: Ctrl+F5

### **Formulário não funciona**
- Verifique o console do navegador para erros JavaScript
- Confirme se as APIs externas (EmailJS, etc.) estão acessíveis

## 📊 Monitoramento

### **Logs importantes para monitorar:**
```bash
# Logs de acesso
tail -f /var/log/apache2/access.log

# Logs de erro
tail -f /var/log/apache2/error.log

# Monitorar conversões
grep "form_submit" /var/log/apache2/access.log
```

### **Métricas para acompanhar:**
- Taxa de conversão do formulário
- Tempo de carregamento da página
- Taxa de rejeição
- Origem do tráfego

## 🚀 Próximos Passos

Após o deploy bem-sucedido:

1. **Configure Google Analytics**
2. **Instale Facebook Pixel**
3. **Configure EmailJS para envio de emails**
4. **Teste A/B diferentes versões**
5. **Configure backup automático**
6. **Monitore performance e conversões**

## 📞 Suporte

Se encontrar problemas durante o deploy:
1. Verifique os logs de erro do Apache
2. Teste em ambiente local primeiro
3. Confirme se todos os módulos necessários estão habilitados
4. Verifique permissões de arquivo

---

**✅ Landing page pronta para gerar conversões máximas!** 🎯
