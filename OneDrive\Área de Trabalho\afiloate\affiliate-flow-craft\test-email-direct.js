// TESTE DIRETO DA API BREVO - DIAGNÓSTICO TOTAL
console.log('🔧 EMERGENCY TEAM: Iniciando teste direto da API Brevo...');

const API_KEY = '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';

async function testBrevoAPI() {
    try {
        console.log('📧 Testando criação de contato...');
        
        // Teste 1: Criar contato
        const contactResponse = await fetch('https://api.brevo.com/v3/contacts', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'api-key': API_KEY
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                attributes: {
                    FIRSTNAME: 'Teste',
                    LASTNAME: 'Emergência',
                    SOURCE: 'teste_direto'
                },
                listIds: [1]
            })
        });

        console.log('📊 Status contato:', contactResponse.status);
        const contactData = await contactResponse.text();
        console.log('📊 Resposta contato:', contactData);

        // Teste 2: Enviar email simples
        console.log('📧 Testando envio de email...');
        
        const emailResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'api-key': API_KEY
            },
            body: JSON.stringify({
                sender: {
                    email: '<EMAIL>',
                    name: 'AffiliateFlow Pro - Teste'
                },
                to: [{ 
                    email: '<EMAIL>', 
                    name: 'Teste Emergência' 
                }],
                subject: '🚨 TESTE EMERGÊNCIA - Sistema de Email',
                htmlContent: `
                    <h1>🚨 TESTE DE EMERGÊNCIA</h1>
                    <p>Este é um teste direto da API Brevo.</p>
                    <p>Se você recebeu este email, o sistema está funcionando!</p>
                    <p>Timestamp: ${new Date().toISOString()}</p>
                `
            })
        });

        console.log('📊 Status email:', emailResponse.status);
        const emailData = await emailResponse.text();
        console.log('📊 Resposta email:', emailData);

        if (emailResponse.ok) {
            console.log('✅ SUCESSO! Email enviado via API Brevo');
            return true;
        } else {
            console.log('❌ FALHA! Erro na API Brevo');
            return false;
        }

    } catch (error) {
        console.error('❌ ERRO CRÍTICO:', error);
        return false;
    }
}

// Executar teste
testBrevoAPI().then(success => {
    if (success) {
        console.log('🎉 DIAGNÓSTICO: API Brevo funcionando!');
    } else {
        console.log('🚨 DIAGNÓSTICO: Problema na API Brevo!');
    }
});
