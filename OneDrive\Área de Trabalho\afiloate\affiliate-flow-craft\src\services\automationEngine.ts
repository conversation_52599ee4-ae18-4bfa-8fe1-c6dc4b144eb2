// ===================================================================
// AUTOMATION ENGINE - AffiliateFlow Pro
// Intelligent automation system for email sequences and triggers
// ===================================================================

import { createLogger, logPerformance } from '../core/utils/logger';
import { config } from '../core/config';
import { ILead, IApiResponse } from '../core/types';
import { brevoService } from './brevoService';
import { aiPersonalization, ITouchpointData } from './aiPersonalization';

const automationLogger = createLogger('AUTOMATION_ENGINE');

// Automation Types
export interface IAutomationRule {
  readonly id: string;
  readonly name: string;
  readonly trigger: ITrigger;
  readonly conditions: ICondition[];
  readonly actions: IAction[];
  readonly isActive: boolean;
  readonly priority: number;
}

export interface ITrigger {
  readonly type: 'time_based' | 'behavior_based' | 'event_based';
  readonly config: Record<string, unknown>;
}

export interface ICondition {
  readonly field: string;
  readonly operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains';
  readonly value: unknown;
}

export interface IAction {
  readonly type: 'send_email' | 'update_profile' | 'add_tag' | 'move_to_list' | 'webhook';
  readonly config: Record<string, unknown>;
}

export interface IAutomationExecution {
  readonly id: string;
  readonly ruleId: string;
  readonly leadId: string;
  readonly timestamp: Date;
  readonly status: 'pending' | 'executing' | 'completed' | 'failed';
  readonly result?: IApiResponse;
  readonly error?: string;
}

// Pre-configured Automation Rules
const DEFAULT_AUTOMATION_RULES: IAutomationRule[] = [
  {
    id: 'welcome_sequence',
    name: 'Welcome Email Sequence',
    trigger: {
      type: 'event_based',
      config: { event: 'lead_captured' }
    },
    conditions: [],
    actions: [
      {
        type: 'send_email',
        config: { 
          template: 'welcome',
          delay: 0 
        }
      }
    ],
    isActive: true,
    priority: 1
  },
  
  {
    id: 'niche_follow_up',
    name: 'Niche-Specific Follow-up',
    trigger: {
      type: 'time_based',
      config: { 
        delay: 2 * 60 * 60 * 1000, // 2 hours
        after_event: 'welcome_sent'
      }
    },
    conditions: [
      {
        field: 'niche',
        operator: 'not_equals',
        value: 'general'
      }
    ],
    actions: [
      {
        type: 'send_email',
        config: { 
          template: 'niche_specific',
          personalized: true
        }
      }
    ],
    isActive: true,
    priority: 2
  },

  {
    id: 'engagement_booster',
    name: 'Low Engagement Booster',
    trigger: {
      type: 'behavior_based',
      config: { 
        condition: 'no_email_open',
        timeframe: 24 * 60 * 60 * 1000 // 24 hours
      }
    },
    conditions: [
      {
        field: 'engagementLevel',
        operator: 'equals',
        value: 'low'
      }
    ],
    actions: [
      {
        type: 'send_email',
        config: { 
          template: 'engagement_booster',
          subject_variant: 'curiosity'
        }
      }
    ],
    isActive: true,
    priority: 3
  },

  {
    id: 'high_intent_accelerator',
    name: 'High Intent Accelerator',
    trigger: {
      type: 'behavior_based',
      config: { 
        condition: 'multiple_email_opens',
        threshold: 3
      }
    },
    conditions: [
      {
        field: 'engagementLevel',
        operator: 'equals',
        value: 'high'
      }
    ],
    actions: [
      {
        type: 'send_email',
        config: { 
          template: 'conversion_accelerator',
          urgency: 'high'
        }
      }
    ],
    isActive: true,
    priority: 4
  },

  {
    id: 'conversion_recovery',
    name: 'Conversion Recovery Sequence',
    trigger: {
      type: 'time_based',
      config: { 
        delay: 7 * 24 * 60 * 60 * 1000, // 7 days
        after_event: 'lead_captured'
      }
    },
    conditions: [
      {
        field: 'conversionStatus',
        operator: 'equals',
        value: 'not_converted'
      }
    ],
    actions: [
      {
        type: 'send_email',
        config: { 
          template: 'final_opportunity',
          urgency: 'high',
          incentive: true
        }
      }
    ],
    isActive: true,
    priority: 5
  }
];

// Automation Engine Class
class AutomationEngine {
  private static instance: AutomationEngine;
  private rules: Map<string, IAutomationRule> = new Map();
  private executions: Map<string, IAutomationExecution> = new Map();
  private scheduledTasks: Map<string, NodeJS.Timeout> = new Map();

  private constructor() {
    this.initializeRules();
    automationLogger.info('Automation Engine initialized');
  }

  public static getInstance(): AutomationEngine {
    if (!AutomationEngine.instance) {
      AutomationEngine.instance = new AutomationEngine();
    }
    return AutomationEngine.instance;
  }

  private initializeRules(): void {
    DEFAULT_AUTOMATION_RULES.forEach(rule => {
      this.rules.set(rule.id, rule);
    });

    automationLogger.info('Automation rules loaded', {
      rulesCount: this.rules.size,
      activeRules: Array.from(this.rules.values()).filter(r => r.isActive).length
    });
  }

  @logPerformance('automation.processLead')
  public async processLead(lead: ILead, event: string = 'lead_captured'): Promise<void> {
    automationLogger.info('Processing lead for automation', {
      leadId: lead.id,
      event,
      niche: lead.niche
    });

    // Find applicable rules for this event
    const applicableRules = this.findApplicableRules(lead, event);
    
    // Execute rules in priority order
    for (const rule of applicableRules) {
      await this.executeRule(rule, lead, event);
    }
  }

  private findApplicableRules(lead: ILead, event: string): IAutomationRule[] {
    const applicableRules: IAutomationRule[] = [];

    for (const rule of this.rules.values()) {
      if (!rule.isActive) continue;

      // Check if trigger matches
      if (this.isTriggerMatched(rule.trigger, event, lead)) {
        // Check if conditions are met
        if (this.areConditionsMet(rule.conditions, lead)) {
          applicableRules.push(rule);
        }
      }
    }

    // Sort by priority
    return applicableRules.sort((a, b) => a.priority - b.priority);
  }

  private isTriggerMatched(trigger: ITrigger, event: string, lead: ILead): boolean {
    switch (trigger.type) {
      case 'event_based':
        return trigger.config.event === event;
      
      case 'behavior_based':
        return this.checkBehaviorTrigger(trigger, lead);
      
      case 'time_based':
        // Time-based triggers are handled separately
        return false;
      
      default:
        return false;
    }
  }

  private checkBehaviorTrigger(trigger: ITrigger, lead: ILead): boolean {
    const profile = aiPersonalization.getProfile(lead.id);
    if (!profile) return false;

    const condition = trigger.config.condition as string;
    
    switch (condition) {
      case 'no_email_open':
        const timeframe = trigger.config.timeframe as number;
        const cutoff = new Date(Date.now() - timeframe);
        return !profile.touchpoints.some(tp => 
          tp.type === 'email_open' && tp.timestamp > cutoff
        );
      
      case 'multiple_email_opens':
        const threshold = trigger.config.threshold as number;
        const opens = profile.touchpoints.filter(tp => tp.type === 'email_open');
        return opens.length >= threshold;
      
      default:
        return false;
    }
  }

  private areConditionsMet(conditions: ICondition[], lead: ILead): boolean {
    if (conditions.length === 0) return true;

    const profile = aiPersonalization.getProfile(lead.id);
    
    return conditions.every(condition => {
      const fieldValue = this.getFieldValue(condition.field, lead, profile);
      return this.evaluateCondition(fieldValue, condition.operator, condition.value);
    });
  }

  private getFieldValue(field: string, lead: ILead, profile?: any): unknown {
    // Get value from lead object
    if (field in lead) {
      return (lead as any)[field];
    }

    // Get value from profile
    if (profile && field in profile) {
      return profile[field];
    }

    // Special computed fields
    switch (field) {
      case 'conversionStatus':
        return lead.status === 'converted' ? 'converted' : 'not_converted';
      
      default:
        return undefined;
    }
  }

  private evaluateCondition(fieldValue: unknown, operator: string, expectedValue: unknown): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === expectedValue;
      
      case 'not_equals':
        return fieldValue !== expectedValue;
      
      case 'greater_than':
        return typeof fieldValue === 'number' && typeof expectedValue === 'number' 
          ? fieldValue > expectedValue : false;
      
      case 'less_than':
        return typeof fieldValue === 'number' && typeof expectedValue === 'number' 
          ? fieldValue < expectedValue : false;
      
      case 'contains':
        return typeof fieldValue === 'string' && typeof expectedValue === 'string'
          ? fieldValue.includes(expectedValue) : false;
      
      case 'not_contains':
        return typeof fieldValue === 'string' && typeof expectedValue === 'string'
          ? !fieldValue.includes(expectedValue) : false;
      
      default:
        return false;
    }
  }

  @logPerformance('automation.executeRule')
  private async executeRule(rule: IAutomationRule, lead: ILead, event: string): Promise<void> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
    
    const execution: IAutomationExecution = {
      id: executionId,
      ruleId: rule.id,
      leadId: lead.id,
      timestamp: new Date(),
      status: 'executing'
    };

    this.executions.set(executionId, execution);

    try {
      automationLogger.info('Executing automation rule', {
        ruleId: rule.id,
        ruleName: rule.name,
        leadId: lead.id,
        executionId
      });

      // Execute all actions
      for (const action of rule.actions) {
        await this.executeAction(action, lead, rule);
      }

      // Mark as completed
      execution.status = 'completed';
      this.executions.set(executionId, execution);

      automationLogger.info('Automation rule executed successfully', {
        ruleId: rule.id,
        executionId,
        actionsCount: rule.actions.length
      });

    } catch (error) {
      execution.status = 'failed';
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      this.executions.set(executionId, execution);

      automationLogger.error('Automation rule execution failed', {
        ruleId: rule.id,
        executionId,
        error: execution.error
      });
    }
  }

  private async executeAction(action: IAction, lead: ILead, rule: IAutomationRule): Promise<void> {
    switch (action.type) {
      case 'send_email':
        await this.executeSendEmailAction(action, lead);
        break;
      
      case 'update_profile':
        await this.executeUpdateProfileAction(action, lead);
        break;
      
      case 'add_tag':
        await this.executeAddTagAction(action, lead);
        break;
      
      default:
        automationLogger.warn('Unknown action type', {
          actionType: action.type,
          ruleId: rule.id
        });
    }
  }

  private async executeSendEmailAction(action: IAction, lead: ILead): Promise<void> {
    const template = action.config.template as string;
    const delay = action.config.delay as number || 0;

    if (delay > 0) {
      // Schedule for later
      setTimeout(async () => {
        await this.sendEmailByTemplate(template, lead, action.config);
      }, delay);
    } else {
      // Send immediately
      await this.sendEmailByTemplate(template, lead, action.config);
    }
  }

  private async sendEmailByTemplate(template: string, lead: ILead, config: Record<string, unknown>): Promise<void> {
    try {
      let result: IApiResponse;

      switch (template) {
        case 'welcome':
          result = await brevoService.sendWelcomeEmail(lead);
          break;
        
        case 'niche_specific':
          result = await brevoService.sendNicheEmail(lead);
          break;
        
        default:
          automationLogger.warn('Unknown email template', { template, leadId: lead.id });
          return;
      }

      if (result.success) {
        // Track email sent event
        const touchpoint: ITouchpointData = {
          type: 'email_open', // Will be updated when actually opened
          timestamp: new Date(),
          metadata: { template, automated: true },
          score: 5
        };

        aiPersonalization.updateProfile(lead.id, touchpoint);
      }

    } catch (error) {
      automationLogger.error('Failed to send automated email', {
        template,
        leadId: lead.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private async executeUpdateProfileAction(action: IAction, lead: ILead): Promise<void> {
    // Implementation for updating profile
    automationLogger.debug('Profile update action executed', {
      leadId: lead.id,
      updates: action.config
    });
  }

  private async executeAddTagAction(action: IAction, lead: ILead): Promise<void> {
    // Implementation for adding tags
    automationLogger.debug('Add tag action executed', {
      leadId: lead.id,
      tag: action.config.tag
    });
  }

  public scheduleTimeBasedRules(lead: ILead): void {
    const timeBasedRules = Array.from(this.rules.values())
      .filter(rule => rule.isActive && rule.trigger.type === 'time_based');

    timeBasedRules.forEach(rule => {
      const delay = rule.trigger.config.delay as number;
      const taskId = `${rule.id}_${lead.id}`;

      const timeout = setTimeout(async () => {
        if (this.areConditionsMet(rule.conditions, lead)) {
          await this.executeRule(rule, lead, 'time_trigger');
        }
        this.scheduledTasks.delete(taskId);
      }, delay);

      this.scheduledTasks.set(taskId, timeout);

      automationLogger.debug('Time-based rule scheduled', {
        ruleId: rule.id,
        leadId: lead.id,
        delay,
        taskId
      });
    });
  }

  public getExecutionStats(): {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    pendingExecutions: number;
  } {
    const executions = Array.from(this.executions.values());
    
    return {
      totalExecutions: executions.length,
      successfulExecutions: executions.filter(e => e.status === 'completed').length,
      failedExecutions: executions.filter(e => e.status === 'failed').length,
      pendingExecutions: executions.filter(e => e.status === 'pending' || e.status === 'executing').length,
    };
  }

  public addRule(rule: IAutomationRule): void {
    this.rules.set(rule.id, rule);
    automationLogger.info('Automation rule added', { ruleId: rule.id, ruleName: rule.name });
  }

  public removeRule(ruleId: string): void {
    this.rules.delete(ruleId);
    automationLogger.info('Automation rule removed', { ruleId });
  }

  public getRules(): IAutomationRule[] {
    return Array.from(this.rules.values());
  }
}

// Export singleton instance
export const automationEngine = AutomationEngine.getInstance();
