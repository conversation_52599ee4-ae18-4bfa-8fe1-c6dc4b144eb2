// ===================================================================
// SIMPLE DEBUG COMPONENT - AffiliateFlow Pro
// Shows system status and logs for debugging
// ===================================================================

import React, { useState, useEffect } from 'react';
import { Bug, CheckCircle, AlertCircle, X } from 'lucide-react';

const SimpleDebug: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [status, setStatus] = useState({
    brevoConfigured: false,
    apiKey: '',
    environment: ''
  });

  useEffect(() => {
    // Check configuration
    const apiKey = import.meta.env.VITE_BREVO_API_KEY || '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';
    const environment = import.meta.env.VITE_ENVIRONMENT || 'development';
    
    setStatus({
      brevoConfigured: !!apiKey && apiKey !== '',
      apiKey: apiKey ? apiKey.substring(0, 20) + '...' : 'none',
      environment
    });

    // Capture console logs
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;

    console.log = (...args) => {
      setLogs(prev => [...prev.slice(-20), `[LOG] ${args.join(' ')}`]);
      originalLog(...args);
    };

    console.error = (...args) => {
      setLogs(prev => [...prev.slice(-20), `[ERROR] ${args.join(' ')}`]);
      originalError(...args);
    };

    console.warn = (...args) => {
      setLogs(prev => [...prev.slice(-20), `[WARN] ${args.join(' ')}`]);
      originalWarn(...args);
    };

    return () => {
      console.log = originalLog;
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-50"
        title="Abrir Debug"
      >
        <Bug className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-xl max-w-md w-full max-h-96 z-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Bug className="w-5 h-5 text-blue-600" />
          <span className="font-medium text-gray-900">Debug Panel</span>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <X className="w-4 h-4 text-gray-500" />
        </button>
      </div>

      {/* Status */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-medium text-gray-900 mb-2">Status do Sistema</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            {status.brevoConfigured ? (
              <CheckCircle className="w-4 h-4 text-green-500" />
            ) : (
              <AlertCircle className="w-4 h-4 text-red-500" />
            )}
            <span>Brevo API: {status.brevoConfigured ? 'Configurado' : 'Não configurado'}</span>
          </div>
          
          <div className="text-xs text-gray-600">
            API Key: {status.apiKey}
          </div>
          
          <div className="text-xs text-gray-600">
            Environment: {status.environment}
          </div>
        </div>
      </div>

      {/* Logs */}
      <div className="p-4">
        <h3 className="font-medium text-gray-900 mb-2">Logs Recentes</h3>
        <div className="bg-gray-50 rounded p-2 max-h-32 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-xs text-gray-500">Nenhum log ainda...</div>
          ) : (
            <div className="space-y-1">
              {logs.slice(-10).map((log, index) => (
                <div key={index} className="text-xs font-mono">
                  {log.includes('[ERROR]') && (
                    <span className="text-red-600">{log}</span>
                  )}
                  {log.includes('[WARN]') && (
                    <span className="text-orange-600">{log}</span>
                  )}
                  {log.includes('[LOG]') && (
                    <span className="text-gray-700">{log}</span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={() => setLogs([])}
          className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded transition-colors"
        >
          Limpar Logs
        </button>
      </div>
    </div>
  );
};

export default SimpleDebug;
