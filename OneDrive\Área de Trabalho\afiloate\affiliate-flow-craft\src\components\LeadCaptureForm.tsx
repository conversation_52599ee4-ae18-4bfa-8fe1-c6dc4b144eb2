// Advanced Lead Capture Form with Smart Validation
import React, { useState, useEffect } from 'react';
import { User, Mail, Phone, Zap, Shield, Gift } from 'lucide-react';
import { captureLeadWithAutomation, validateEmail, validatePhone, formatPhone } from '../services/leadCapture';
import { emailMarketing } from '../services/emailMarketing';

interface LeadCaptureFormProps {
  variant?: 'hero' | 'popup' | 'inline' | 'exit-intent';
  source?: string;
  onSuccess?: (data: any) => void;
  onClose?: () => void;
  showBonuses?: boolean;
}

const LeadCaptureForm: React.FC<LeadCaptureFormProps> = ({
  variant = 'hero',
  source = 'landing-page',
  onSuccess,
  onClose,
  showBonuses = true
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: ''
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [step, setStep] = useState(1);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Nome deve ter pelo menos 2 caracteres';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = 'Telefone inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      console.log('🚀 Processando lead...');

      // Simular processamento
      await new Promise(resolve => setTimeout(resolve, 2000));

      const leadData = {
        name: formData.name.trim(),
        email: formData.email.toLowerCase().trim(),
        phone: formData.phone ? formatPhone(formData.phone) : '',
        source: source || 'localhost-3000'
      };

      console.log('✅ Lead processado:', leadData);

      // Salvar no localStorage
      const leads = JSON.parse(localStorage.getItem('affiliate_leads') || '[]');
      leads.push({
        ...leadData,
        timestamp: new Date().toISOString(),
        id: `lead_${Date.now()}`
      });
      localStorage.setItem('affiliate_leads', JSON.stringify(leads));

      setIsSuccess(true);
      setStep(2);

      // Call success callback
      if (onSuccess) {
        onSuccess({
          leadId: `lead_${Date.now()}`,
          affiliateLink: 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368',
          emailSent: true
        });
      }

      // Auto-close popup after success (if applicable)
      if (variant === 'popup' || variant === 'exit-intent') {
        setTimeout(() => {
          if (onClose) onClose();
        }, 5000);
      }

    } catch (error) {
      setErrors({ submit: 'Erro inesperado. Tente novamente.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Format phone number in real-time
    if (field === 'phone') {
      setFormData(prev => ({ ...prev, phone: formatPhone(value) }));
    }
  };

  // Success state
  if (isSuccess && step === 2) {
    return (
      <div className="glass-dark rounded-3xl p-8 max-w-md mx-auto text-center animate-scale-in">
        <div className="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
          <Gift className="w-8 h-8 text-white" />
        </div>
        
        <h3 className="text-2xl font-bold text-lp-light mb-4">
          🎉 Parabéns! Você está dentro!
        </h3>
        
        <p className="text-lp-light/80 mb-6">
          Seu link de afiliado está sendo preparado e você receberá todas as instruções por email em instantes.
        </p>
        
        <div className="space-y-3 text-sm text-lp-light/70">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-lp-green rounded-full"></div>
            <span>Email de boas-vindas enviado</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-lp-orange rounded-full animate-pulse"></div>
            <span>Preparando seu link exclusivo...</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-lp-purple rounded-full"></div>
            <span>Acesso aos materiais liberado</span>
          </div>
        </div>

        {(variant === 'popup' || variant === 'exit-intent') && (
          <button
            onClick={onClose}
            className="mt-6 btn-outline text-sm px-6 py-2"
          >
            Fechar
          </button>
        )}
      </div>
    );
  }

  // Main form
  return (
    <div className={`
      ${variant === 'hero' ? 'max-w-lg mx-auto' : ''}
      ${variant === 'popup' || variant === 'exit-intent' ? 'max-w-md mx-auto' : ''}
      ${variant === 'inline' ? 'max-w-sm' : ''}
    `}>
      <form onSubmit={handleSubmit} className="glass-dark rounded-3xl p-8 space-y-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mx-auto mb-4">
            <Zap className="w-6 h-6 text-white" />
          </div>
          
          <h3 className="text-2xl font-bold text-lp-light mb-2">
            {variant === 'exit-intent' ? '⚡ Espere! Não Perca Esta Oportunidade!' : 'Quero Meu Link de Afiliado'}
          </h3>
          
          <p className="text-lp-light/70 text-sm">
            {variant === 'exit-intent' 
              ? 'Últimas 3 vagas para o sistema que gera R$ 15k/mês'
              : 'Preencha os dados e receba acesso imediato'
            }
          </p>
        </div>

        {/* Niche Selector (if enabled) */}
        {showNicheSelector && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-lp-light mb-3">
              🎯 Qual é o seu nicho principal?
            </label>
            <div className="grid grid-cols-2 gap-3">
              {[
                { value: UserNiche.TECH, icon: Code, label: 'Tech/Dev', color: 'blue' },
                { value: UserNiche.FINANCE, icon: DollarSign, label: 'Finanças', color: 'green' },
                { value: UserNiche.BUSINESS, icon: Briefcase, label: 'Business', color: 'orange' },
                { value: UserNiche.AI, icon: Brain, label: 'IA/Tech', color: 'purple' }
              ].map((niche) => {
                const Icon = niche.icon;
                const isSelected = formData.niche === niche.value;
                return (
                  <button
                    key={niche.value}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, niche: niche.value }))}
                    className={`
                      p-3 rounded-xl border-2 transition-all duration-200 text-left
                      ${isSelected
                        ? `border-lp-${niche.color} bg-lp-${niche.color}/10`
                        : 'border-lp-light/20 hover:border-lp-light/40'
                      }
                    `}
                  >
                    <div className="flex items-center gap-2">
                      <Icon className={`w-4 h-4 ${isSelected ? `text-lp-${niche.color}` : 'text-lp-light/60'}`} />
                      <span className={`text-sm font-medium ${isSelected ? 'text-lp-light' : 'text-lp-light/70'}`}>
                        {niche.label}
                      </span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Bonuses (if enabled) */}
        {showBonuses && (
          <div className="bg-gradient-to-r from-lp-orange/10 to-lp-green/10 rounded-2xl p-4 mb-6">
            <div className="text-center">
              <div className="text-sm font-semibold text-lp-orange mb-2">🎁 BÔNUS EXCLUSIVOS</div>
              <div className="text-xs text-lp-light/80 space-y-1">
                <div>✅ E-book: 7 Segredos dos Afiliados</div>
                <div>✅ Scripts Prontos para Redes Sociais</div>
                <div>✅ Planilha de Controle de Comissões</div>
              </div>
            </div>
          </div>
        )}

        {/* Form Fields */}
        <div className="space-y-4">
          {/* Name Field */}
          <div>
            <div className="relative">
              <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-lp-light/40" />
              <input
                type="text"
                placeholder="Seu nome completo"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`
                  w-full pl-12 pr-4 py-4 rounded-2xl bg-lp-gray/50 border-2 
                  text-lp-light placeholder-lp-light/40 transition-all duration-300
                  focus:outline-none focus:border-lp-purple focus:bg-lp-gray/70
                  ${errors.name ? 'border-red-500' : 'border-transparent'}
                `}
                disabled={isLoading}
              />
            </div>
            {errors.name && (
              <p className="text-red-400 text-xs mt-2 ml-2">{errors.name}</p>
            )}
          </div>

          {/* Email Field */}
          <div>
            <div className="relative">
              <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-lp-light/40" />
              <input
                type="email"
                placeholder="Seu melhor email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`
                  w-full pl-12 pr-4 py-4 rounded-2xl bg-lp-gray/50 border-2 
                  text-lp-light placeholder-lp-light/40 transition-all duration-300
                  focus:outline-none focus:border-lp-purple focus:bg-lp-gray/70
                  ${errors.email ? 'border-red-500' : 'border-transparent'}
                `}
                disabled={isLoading}
              />
            </div>
            {errors.email && (
              <p className="text-red-400 text-xs mt-2 ml-2">{errors.email}</p>
            )}
          </div>

          {/* Phone Field (Optional) */}
          <div>
            <div className="relative">
              <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-lp-light/40" />
              <input
                type="tel"
                placeholder="WhatsApp (opcional)"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`
                  w-full pl-12 pr-4 py-4 rounded-2xl bg-lp-gray/50 border-2 
                  text-lp-light placeholder-lp-light/40 transition-all duration-300
                  focus:outline-none focus:border-lp-purple focus:bg-lp-gray/70
                  ${errors.phone ? 'border-red-500' : 'border-transparent'}
                `}
                disabled={isLoading}
              />
            </div>
            {errors.phone && (
              <p className="text-red-400 text-xs mt-2 ml-2">{errors.phone}</p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading}
          className={`
            w-full btn-primary py-4 text-lg font-semibold rounded-2xl
            disabled:opacity-50 disabled:cursor-not-allowed
            ${isLoading ? 'animate-pulse-glow' : ''}
            transform transition-all duration-300
            hover:scale-105 active:scale-95
          `}
        >
          {isLoading ? (
            <span className="flex items-center justify-center gap-3">
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              Processando...
            </span>
          ) : (
            <span className="flex items-center justify-center gap-3">
              <Zap className="w-5 h-5" />
              {variant === 'exit-intent' ? 'GARANTIR MINHA VAGA' : 'QUERO MEU LINK AGORA'}
            </span>
          )}
        </button>

        {/* Error Message */}
        {errors.submit && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-2xl p-4">
            <p className="text-red-400 text-sm text-center">{errors.submit}</p>
          </div>
        )}

        {/* Security Badge */}
        <div className="flex items-center justify-center gap-2 text-xs text-lp-light/50">
          <Shield className="w-4 h-4" />
          <span>Seus dados estão 100% seguros</span>
        </div>

        {/* Close button for popups */}
        {(variant === 'popup' || variant === 'exit-intent') && onClose && (
          <button
            type="button"
            onClick={onClose}
            className="absolute top-4 right-4 text-lp-light/40 hover:text-lp-light/80 transition-colors"
          >
            ✕
          </button>
        )}
      </form>
    </div>
  );
};

export default LeadCaptureForm;
