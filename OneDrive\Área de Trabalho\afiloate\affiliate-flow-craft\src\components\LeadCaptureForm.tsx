// Advanced Lead Capture Form with Smart Validation
import React, { useState, useEffect } from 'react';
import { User, Mail, Phone, Zap, Shield, Gift } from 'lucide-react';
import { captureLeadWithAutomation, validateEmail, validatePhone, formatPhone } from '../services/leadCapture';
import { emailMarketing } from '../services/emailMarketing';

interface LeadCaptureFormProps {
  variant?: 'hero' | 'popup' | 'inline' | 'exit-intent';
  source?: string;
  onSuccess?: (data: any) => void;
  onClose?: () => void;
  showBonuses?: boolean;
}

const LeadCaptureForm: React.FC<LeadCaptureFormProps> = ({
  variant = 'hero',
  source = 'landing-page',
  onSuccess,
  onClose,
  showBonuses = true
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: ''
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [step, setStep] = useState(1);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Nome deve ter pelo menos 2 caracteres';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = 'Telefone inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      console.log('🔥 MASTER SYSTEM: Iniciando operação mestra');

      const leadData = {
        name: formData.name.trim(),
        email: formData.email.toLowerCase().trim(),
        phone: formData.phone ? formatPhone(formData.phone) : undefined,
        source: source || 'landing-page',
        timestamp: new Date().toISOString()
      };

      console.log('✅ MASTER: Lead data created', leadData);

      // SISTEMA MESTRE - MÚLTIPLAS TENTATIVAS
      let emailSent = false;
      let lastError = '';

      // TENTATIVA 1: EmailJS (mais confiável)
      console.log('🚀 MASTER: Tentativa 1 - EmailJS');
      try {
        const emailJSResult = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            service_id: 'service_default',
            template_id: 'template_default',
            user_id: 'user_default',
            template_params: {
              to_email: leadData.email,
              to_name: leadData.name,
              from_name: 'AffiliateFlow Pro',
              subject: '🤖 Seu Kit GRIP - Renda Recorrente com IA',
              message: `
                Olá ${leadData.name.split(' ')[0]}!

                🎉 PARABÉNS! Você agora faz parte do seleto grupo de influencers que geram RENDA RECORRENTE indicando IA para empresas!

                🤖 SOBRE A GRIP:
                ✅ Soluções de IA financeira para empresas
                ✅ Planos de R$ 15.000/mês até R$ 55.000 (Enterprise)
                ✅ Comissões recorrentes para afiliados

                💰 SEU POTENCIAL DE GANHOS:
                • Base: R$ 15.000/mês recorrente por empresa indicada
                • Enterprise: Até R$ 55.000 quando empresa contrata plano premium
                • Escalável: Quanto mais empresas, maior sua renda mensal

                🔗 SEU LINK DE AFILIADO:
                https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368

                📱 BAIXE O APP GRIP:
                Android: https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share
                iOS: https://apps.apple.com/us/app/grip-gaiodataos/id6743857628

                Sucesso e bons ganhos! 💪
                Equipe AffiliateFlow Pro
              `
            }
          })
        });

        if (emailJSResult.ok) {
          console.log('✅ MASTER: EmailJS SUCCESS!');
          emailSent = true;
        } else {
          throw new Error('EmailJS failed');
        }
      } catch (emailJSError) {
        console.log('⚠️ MASTER: EmailJS failed, trying Brevo...');
        lastError = emailJSError.message;
      }

      // TENTATIVA 2: Brevo (se EmailJS falhar)
      if (!emailSent) {
        console.log('🚀 MASTER: Tentativa 2 - Brevo API');
        try {
          // Criar contato primeiro
          const contactResponse = await fetch('https://api.brevo.com/v3/contacts', {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'api-key': '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'
            },
            body: JSON.stringify({
              email: leadData.email,
              attributes: {
                FIRSTNAME: leadData.name.split(' ')[0],
                LASTNAME: leadData.name.split(' ').slice(1).join(' ') || '',
                SOURCE: leadData.source
              },
              listIds: [1]
            })
          });

          console.log('📊 MASTER: Brevo contact status:', contactResponse.status);

      // Enviar email com sender verificado
      const emailResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': '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'
        },
        body: JSON.stringify({
          sender: {
            email: '<EMAIL>',
            name: 'AffiliateFlow Pro'
          },
          to: [{
            email: leadData.email,
            name: leadData.name
          }],
          subject: '🤖 Seu Kit GRIP - Renda Recorrente com IA Empresarial',
          htmlContent: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 20px; border-radius: 15px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: white; margin-bottom: 10px; font-size: 28px;">🎉 Parabéns, ${leadData.name.split(' ')[0]}!</h1>
                <p style="font-size: 18px; opacity: 0.9; line-height: 1.5;">Você agora faz parte do seleto grupo de influencers que geram <strong>RENDA RECORRENTE</strong> indicando IA para empresas!</p>
              </div>

              <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin-bottom: 25px;">
                <h2 style="color: #FFD700; margin-bottom: 15px; font-size: 22px;">🤖 SOBRE A GRIP - PLATAFORMA DE IA EMPRESARIAL:</h2>
                <ul style="list-style: none; padding: 0; margin: 0;">
                  <li style="margin-bottom: 10px; padding-left: 0;">✅ Soluções de IA financeira para empresas</li>
                  <li style="margin-bottom: 10px; padding-left: 0;">✅ Planos de R$ 15.000/mês até R$ 55.000 (Enterprise)</li>
                  <li style="margin-bottom: 10px; padding-left: 0;">✅ Comissões recorrentes para afiliados</li>
                  <li style="margin-bottom: 10px; padding-left: 0;">✅ Suporte técnico especializado</li>
                </ul>
              </div>

              <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin-bottom: 25px;">
                <h2 style="color: #00FF88; margin-bottom: 15px; font-size: 22px;">💰 SEU POTENCIAL DE GANHOS:</h2>
                <ul style="list-style: none; padding: 0; margin: 0;">
                  <li style="margin-bottom: 10px; padding-left: 0;">• <strong>Base:</strong> R$ 15.000/mês recorrente por empresa indicada</li>
                  <li style="margin-bottom: 10px; padding-left: 0;">• <strong>Enterprise:</strong> Até R$ 55.000 quando empresa contrata plano premium</li>
                  <li style="margin-bottom: 10px; padding-left: 0;">• <strong>Escalável:</strong> Quanto mais empresas, maior sua renda mensal</li>
                </ul>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <h2 style="color: #FFD700; margin-bottom: 15px; font-size: 22px;">🔗 SEU LINK DE AFILIADO:</h2>
                <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368"
                   style="display: inline-block; background: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold; font-size: 16px; margin: 10px 0;">
                  🚀 ACESSAR LINK GRIP
                </a>
              </div>

              <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin-bottom: 25px;">
                <h2 style="color: #FFD700; margin-bottom: 15px; font-size: 22px;">📱 BAIXE O APP GRIP:</h2>
                <div style="text-align: center;">
                  <a href="https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share"
                     style="display: inline-block; background: #34A853; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 5px; font-weight: bold;">
                    📱 Android
                  </a>
                  <a href="https://apps.apple.com/us/app/grip-gaiodataos/id6743857628"
                     style="display: inline-block; background: #007AFF; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 5px; font-weight: bold;">
                    📱 iOS
                  </a>
                </div>
              </div>

              <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.3);">
                <p style="opacity: 0.8; margin-bottom: 10px;">Sucesso e bons ganhos! 💪</p>
                <p style="font-weight: bold; margin: 0;">Equipe AffiliateFlow Pro</p>
              </div>
            </div>
          `
        })
      });

      console.log('📊 Email send status:', emailResponse.status);

      if (emailResponse.ok) {
        console.log('✅ Email sent successfully!');

        setIsSuccess(true);
        setStep(2);

        if (onSuccess) {
          onSuccess(leadData);
        }

        if (variant === 'popup' || variant === 'exit-intent') {
          setTimeout(() => {
            if (onClose) onClose();
          }, 5000);
        }

      } else {
        const errorText = await emailResponse.text();
        console.log('❌ Email failed:', errorText);
        console.log('❌ Full error response:', emailResponse);
        setErrors({ submit: 'Email salvo mas não enviado. Verifique o console.' });
      }

    } catch (error) {
      console.error('❌ Critical error:', error);
      setErrors({ submit: 'Erro crítico. Verifique o console.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Format phone number in real-time
    if (field === 'phone') {
      setFormData(prev => ({ ...prev, phone: formatPhone(value) }));
    }
  };

  // Success state
  if (isSuccess && step === 2) {
    return (
      <div className="glass-dark rounded-3xl p-8 max-w-md mx-auto text-center animate-scale-in">
        <div className="w-16 h-16 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
          <Gift className="w-8 h-8 text-white" />
        </div>
        
        <h3 className="text-2xl font-bold text-lp-light mb-4">
          🎉 Parabéns! Você está dentro!
        </h3>
        
        <p className="text-lp-light/80 mb-6">
          Seu link de afiliado está sendo preparado e você receberá todas as instruções por email em instantes.
        </p>
        
        <div className="space-y-3 text-sm text-lp-light/70">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-lp-green rounded-full"></div>
            <span>Email de boas-vindas enviado</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-lp-orange rounded-full animate-pulse"></div>
            <span>Preparando seu link exclusivo...</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-lp-purple rounded-full"></div>
            <span>Acesso aos materiais liberado</span>
          </div>
        </div>

        {(variant === 'popup' || variant === 'exit-intent') && (
          <button
            onClick={onClose}
            className="mt-6 btn-outline text-sm px-6 py-2"
          >
            Fechar
          </button>
        )}
      </div>
    );
  }

  // Main form
  return (
    <div className={`
      ${variant === 'hero' ? 'max-w-lg mx-auto' : ''}
      ${variant === 'popup' || variant === 'exit-intent' ? 'max-w-md mx-auto' : ''}
      ${variant === 'inline' ? 'max-w-sm' : ''}
    `}>
      <form onSubmit={handleSubmit} className="glass-dark rounded-3xl p-8 space-y-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mx-auto mb-4">
            <Zap className="w-6 h-6 text-white" />
          </div>
          
          <h3 className="text-2xl font-bold text-lp-light mb-2">
            {variant === 'exit-intent' ? '⚡ Espere! Não Perca Esta Oportunidade!' : 'Quero Meu Link de Afiliado'}
          </h3>
          
          <p className="text-lp-light/70 text-sm">
            {variant === 'exit-intent' 
              ? 'Últimas 3 vagas para o sistema que gera R$ 15k/mês'
              : 'Preencha os dados e receba acesso imediato'
            }
          </p>
        </div>

        {/* Niche Selector (if enabled) */}
        {showNicheSelector && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-lp-light mb-3">
              🎯 Qual é o seu nicho principal?
            </label>
            <div className="grid grid-cols-2 gap-3">
              {[
                { value: UserNiche.TECH, icon: Code, label: 'Tech/Dev', color: 'blue' },
                { value: UserNiche.FINANCE, icon: DollarSign, label: 'Finanças', color: 'green' },
                { value: UserNiche.BUSINESS, icon: Briefcase, label: 'Business', color: 'orange' },
                { value: UserNiche.AI, icon: Brain, label: 'IA/Tech', color: 'purple' }
              ].map((niche) => {
                const Icon = niche.icon;
                const isSelected = formData.niche === niche.value;
                return (
                  <button
                    key={niche.value}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, niche: niche.value }))}
                    className={`
                      p-3 rounded-xl border-2 transition-all duration-200 text-left
                      ${isSelected
                        ? `border-lp-${niche.color} bg-lp-${niche.color}/10`
                        : 'border-lp-light/20 hover:border-lp-light/40'
                      }
                    `}
                  >
                    <div className="flex items-center gap-2">
                      <Icon className={`w-4 h-4 ${isSelected ? `text-lp-${niche.color}` : 'text-lp-light/60'}`} />
                      <span className={`text-sm font-medium ${isSelected ? 'text-lp-light' : 'text-lp-light/70'}`}>
                        {niche.label}
                      </span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Bonuses (if enabled) */}
        {showBonuses && (
          <div className="bg-gradient-to-r from-lp-orange/10 to-lp-green/10 rounded-2xl p-4 mb-6">
            <div className="text-center">
              <div className="text-sm font-semibold text-lp-orange mb-2">🎁 BÔNUS EXCLUSIVOS</div>
              <div className="text-xs text-lp-light/80 space-y-1">
                <div>✅ E-book: 7 Segredos dos Afiliados</div>
                <div>✅ Scripts Prontos para Redes Sociais</div>
                <div>✅ Planilha de Controle de Comissões</div>
              </div>
            </div>
          </div>
        )}

        {/* Form Fields */}
        <div className="space-y-4">
          {/* Name Field */}
          <div>
            <div className="relative">
              <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-lp-light/40" />
              <input
                type="text"
                placeholder="Seu nome completo"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`
                  w-full pl-12 pr-4 py-4 rounded-2xl bg-lp-gray/50 border-2 
                  text-lp-light placeholder-lp-light/40 transition-all duration-300
                  focus:outline-none focus:border-lp-purple focus:bg-lp-gray/70
                  ${errors.name ? 'border-red-500' : 'border-transparent'}
                `}
                disabled={isLoading}
              />
            </div>
            {errors.name && (
              <p className="text-red-400 text-xs mt-2 ml-2">{errors.name}</p>
            )}
          </div>

          {/* Email Field */}
          <div>
            <div className="relative">
              <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-lp-light/40" />
              <input
                type="email"
                placeholder="Seu melhor email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`
                  w-full pl-12 pr-4 py-4 rounded-2xl bg-lp-gray/50 border-2 
                  text-lp-light placeholder-lp-light/40 transition-all duration-300
                  focus:outline-none focus:border-lp-purple focus:bg-lp-gray/70
                  ${errors.email ? 'border-red-500' : 'border-transparent'}
                `}
                disabled={isLoading}
              />
            </div>
            {errors.email && (
              <p className="text-red-400 text-xs mt-2 ml-2">{errors.email}</p>
            )}
          </div>

          {/* Phone Field (Optional) */}
          <div>
            <div className="relative">
              <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-lp-light/40" />
              <input
                type="tel"
                placeholder="WhatsApp (opcional)"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`
                  w-full pl-12 pr-4 py-4 rounded-2xl bg-lp-gray/50 border-2 
                  text-lp-light placeholder-lp-light/40 transition-all duration-300
                  focus:outline-none focus:border-lp-purple focus:bg-lp-gray/70
                  ${errors.phone ? 'border-red-500' : 'border-transparent'}
                `}
                disabled={isLoading}
              />
            </div>
            {errors.phone && (
              <p className="text-red-400 text-xs mt-2 ml-2">{errors.phone}</p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading}
          className={`
            w-full btn-primary py-4 text-lg font-semibold rounded-2xl
            disabled:opacity-50 disabled:cursor-not-allowed
            ${isLoading ? 'animate-pulse-glow' : ''}
            transform transition-all duration-300
            hover:scale-105 active:scale-95
          `}
        >
          {isLoading ? (
            <span className="flex items-center justify-center gap-3">
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              Processando...
            </span>
          ) : (
            <span className="flex items-center justify-center gap-3">
              <Zap className="w-5 h-5" />
              {variant === 'exit-intent' ? 'GARANTIR MINHA VAGA' : 'QUERO MEU LINK AGORA'}
            </span>
          )}
        </button>

        {/* Error Message */}
        {errors.submit && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-2xl p-4">
            <p className="text-red-400 text-sm text-center">{errors.submit}</p>
          </div>
        )}

        {/* Security Badge */}
        <div className="flex items-center justify-center gap-2 text-xs text-lp-light/50">
          <Shield className="w-4 h-4" />
          <span>Seus dados estão 100% seguros</span>
        </div>

        {/* Close button for popups */}
        {(variant === 'popup' || variant === 'exit-intent') && onClose && (
          <button
            type="button"
            onClick={onClose}
            className="absolute top-4 right-4 text-lp-light/40 hover:text-lp-light/80 transition-colors"
          >
            ✕
          </button>
        )}
      </form>
    </div>
  );
};

export default LeadCaptureForm;
