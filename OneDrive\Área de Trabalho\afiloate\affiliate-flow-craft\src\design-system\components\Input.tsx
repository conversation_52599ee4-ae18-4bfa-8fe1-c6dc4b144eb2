// ===================================================================
// INPUT COMPONENT - AffiliateFlow Pro Design System
// Enterprise-grade form inputs with validation and accessibility
// ===================================================================

import React, { forwardRef, InputHTMLAttributes, useState } from 'react';
import { createLogger } from '../../core/utils/logger';
import { theme } from '../tokens';

const inputLogger = createLogger('INPUT_COMPONENT');

// Input Types
export type InputVariant = 'default' | 'filled' | 'outlined' | 'ghost';
export type InputSize = 'sm' | 'md' | 'lg';
export type InputState = 'default' | 'error' | 'success' | 'warning';

// Input Props Interface
export interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  variant?: InputVariant;
  size?: InputSize;
  state?: InputState;
  label?: string;
  helperText?: string;
  errorMessage?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  leftAddon?: React.ReactNode;
  rightAddon?: React.ReactNode;
  isRequired?: boolean;
  isDisabled?: boolean;
  isReadOnly?: boolean;
  fullWidth?: boolean;
  'data-testid'?: string;
}

// Variant Styles
const variantStyles: Record<InputVariant, string> = {
  default: `
    border border-gray-300 
    bg-white 
    focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20
    hover:border-gray-400
  `,
  filled: `
    border-0 
    bg-gray-100 
    focus:bg-white focus:ring-2 focus:ring-blue-500/20
    hover:bg-gray-50
  `,
  outlined: `
    border-2 border-gray-300 
    bg-transparent 
    focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20
    hover:border-gray-400
  `,
  ghost: `
    border-0 
    bg-transparent 
    focus:bg-gray-50 focus:ring-2 focus:ring-blue-500/20
    hover:bg-gray-50
  `,
};

// Size Styles
const sizeStyles: Record<InputSize, string> = {
  sm: 'px-3 py-1.5 text-sm rounded-md min-h-[32px]',
  md: 'px-4 py-2 text-base rounded-lg min-h-[40px]',
  lg: 'px-5 py-3 text-lg rounded-lg min-h-[48px]',
};

// State Styles
const stateStyles: Record<InputState, string> = {
  default: '',
  error: 'border-red-500 focus:border-red-500 focus:ring-red-500/20',
  success: 'border-green-500 focus:border-green-500 focus:ring-green-500/20',
  warning: 'border-yellow-500 focus:border-yellow-500 focus:ring-yellow-500/20',
};

// State Colors for helper text
const stateColors: Record<InputState, string> = {
  default: 'text-gray-600',
  error: 'text-red-600',
  success: 'text-green-600',
  warning: 'text-yellow-600',
};

// Main Input Component
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      variant = 'default',
      size = 'md',
      state = 'default',
      label,
      helperText,
      errorMessage,
      leftIcon,
      rightIcon,
      leftAddon,
      rightAddon,
      isRequired = false,
      isDisabled = false,
      isReadOnly = false,
      fullWidth = false,
      className = '',
      id,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = useState(false);
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    // Determine final state
    const finalState = errorMessage ? 'error' : state;

    // Build CSS classes
    const baseClasses = `
      w-full
      font-medium
      placeholder-gray-400
      transition-all duration-200 ease-out
      focus:outline-none
      disabled:cursor-not-allowed disabled:opacity-50
      read-only:cursor-default read-only:bg-gray-50
    `;

    const classes = [
      baseClasses,
      variantStyles[variant],
      sizeStyles[size],
      stateStyles[finalState],
      fullWidth ? 'w-full' : '',
      isDisabled ? 'opacity-50 cursor-not-allowed' : '',
      isReadOnly ? 'bg-gray-50 cursor-default' : '',
      className,
    ]
      .filter(Boolean)
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Handle focus events
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      props.onFocus?.(e);
      
      inputLogger.debug('Input focused', {
        inputId,
        variant,
        size,
        state: finalState,
      });
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      props.onBlur?.(e);
    };

    // Render input with addons/icons
    const renderInput = () => {
      const hasLeftContent = leftIcon || leftAddon;
      const hasRightContent = rightIcon || rightAddon;

      if (!hasLeftContent && !hasRightContent) {
        return (
          <input
            ref={ref}
            id={inputId}
            className={classes}
            disabled={isDisabled}
            readOnly={isReadOnly}
            required={isRequired}
            onFocus={handleFocus}
            onBlur={handleBlur}
            data-testid={testId}
            {...props}
          />
        );
      }

      return (
        <div className="relative">
          {/* Left Content */}
          {hasLeftContent && (
            <div className="absolute left-0 top-0 h-full flex items-center">
              {leftAddon ? (
                <div className="px-3 border-r border-gray-300 bg-gray-50 h-full flex items-center rounded-l-lg">
                  {leftAddon}
                </div>
              ) : (
                <div className="pl-3">
                  {leftIcon}
                </div>
              )}
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            id={inputId}
            className={`${classes} ${hasLeftContent ? (leftAddon ? 'pl-20' : 'pl-10') : ''} ${
              hasRightContent ? (rightAddon ? 'pr-20' : 'pr-10') : ''
            }`}
            disabled={isDisabled}
            readOnly={isReadOnly}
            required={isRequired}
            onFocus={handleFocus}
            onBlur={handleBlur}
            data-testid={testId}
            {...props}
          />

          {/* Right Content */}
          {hasRightContent && (
            <div className="absolute right-0 top-0 h-full flex items-center">
              {rightAddon ? (
                <div className="px-3 border-l border-gray-300 bg-gray-50 h-full flex items-center rounded-r-lg">
                  {rightAddon}
                </div>
              ) : (
                <div className="pr-3">
                  {rightIcon}
                </div>
              )}
            </div>
          )}
        </div>
      );
    };

    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {/* Label */}
        {label && (
          <label
            htmlFor={inputId}
            className={`
              block text-sm font-medium mb-2
              ${finalState === 'error' ? 'text-red-700' : 'text-gray-700'}
              ${isRequired ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
            `}
          >
            {label}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {renderInput()}
          
          {/* Focus Ring Enhancement */}
          {isFocused && (
            <div className="absolute inset-0 rounded-lg ring-2 ring-blue-500/20 pointer-events-none" />
          )}
        </div>

        {/* Helper Text / Error Message */}
        {(helperText || errorMessage) && (
          <p className={`mt-2 text-sm ${stateColors[finalState]}`}>
            {errorMessage || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// Textarea Component
export interface TextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'> {
  variant?: InputVariant;
  size?: InputSize;
  state?: InputState;
  label?: string;
  helperText?: string;
  errorMessage?: string;
  isRequired?: boolean;
  isDisabled?: boolean;
  isReadOnly?: boolean;
  fullWidth?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  'data-testid'?: string;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      variant = 'default',
      size = 'md',
      state = 'default',
      label,
      helperText,
      errorMessage,
      isRequired = false,
      isDisabled = false,
      isReadOnly = false,
      fullWidth = false,
      resize = 'vertical',
      className = '',
      id,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const finalState = errorMessage ? 'error' : state;

    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize',
    };

    const classes = [
      'w-full font-medium placeholder-gray-400 transition-all duration-200 ease-out focus:outline-none',
      'disabled:cursor-not-allowed disabled:opacity-50 read-only:cursor-default read-only:bg-gray-50',
      variantStyles[variant],
      sizeStyles[size],
      stateStyles[finalState],
      resizeClasses[resize],
      'min-h-[80px]',
      className,
    ]
      .filter(Boolean)
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {/* Label */}
        {label && (
          <label
            htmlFor={textareaId}
            className={`
              block text-sm font-medium mb-2
              ${finalState === 'error' ? 'text-red-700' : 'text-gray-700'}
              ${isRequired ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
            `}
          >
            {label}
          </label>
        )}

        {/* Textarea */}
        <textarea
          ref={ref}
          id={textareaId}
          className={classes}
          disabled={isDisabled}
          readOnly={isReadOnly}
          required={isRequired}
          data-testid={testId}
          {...props}
        />

        {/* Helper Text / Error Message */}
        {(helperText || errorMessage) && (
          <p className={`mt-2 text-sm ${stateColors[finalState]}`}>
            {errorMessage || helperText}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

// Input Group Component for related inputs
export interface InputGroupProps {
  children: React.ReactNode;
  label?: string;
  helperText?: string;
  errorMessage?: string;
  isRequired?: boolean;
  orientation?: 'horizontal' | 'vertical';
  spacing?: keyof typeof theme.spacing;
  className?: string;
}

export const InputGroup: React.FC<InputGroupProps> = ({
  children,
  label,
  helperText,
  errorMessage,
  isRequired = false,
  orientation = 'vertical',
  spacing = '4',
  className = '',
}) => {
  const orientationClasses = {
    horizontal: `flex flex-row space-x-${spacing}`,
    vertical: `flex flex-col space-y-${spacing}`,
  };

  const finalState = errorMessage ? 'error' : 'default';

  return (
    <div className={className}>
      {/* Group Label */}
      {label && (
        <div className={`
          block text-sm font-medium mb-3
          ${finalState === 'error' ? 'text-red-700' : 'text-gray-700'}
          ${isRequired ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
        `}>
          {label}
        </div>
      )}

      {/* Input Container */}
      <div className={orientationClasses[orientation]}>
        {children}
      </div>

      {/* Group Helper Text / Error Message */}
      {(helperText || errorMessage) && (
        <p className={`mt-2 text-sm ${stateColors[finalState]}`}>
          {errorMessage || helperText}
        </p>
      )}
    </div>
  );
};

inputLogger.info('Input components initialized');

// Export all input-related components and types
export default Input;
export { Textarea, InputGroup };
export type { InputVariant, InputSize, InputState };
