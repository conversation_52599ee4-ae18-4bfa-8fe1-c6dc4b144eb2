// Lead Capture Service with Brevo Integration
import { BrevoEmailService } from './brevoService';

export interface LeadData {
  name: string;
  email: string;
  phone?: string;
  source?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

export interface LeadCaptureResponse {
  success: boolean;
  message: string;
  data?: {
    leadId: string;
    affiliateLink: string;
    emailSent: boolean;
  };
  error?: string;
}

// Capture lead with full automation
export async function captureLeadWithAutomation(leadData: LeadData): Promise<LeadCaptureResponse> {
  try {
    // Add UTM parameters from URL
    const urlParams = new URLSearchParams(window.location.search);
    const enrichedLeadData = {
      ...leadData,
      utm_source: leadData.utm_source || urlParams.get('utm_source') || '',
      utm_medium: leadData.utm_medium || urlParams.get('utm_medium') || '',
      utm_campaign: leadData.utm_campaign || urlParams.get('utm_campaign') || '',
      source: leadData.source || 'landing-page'
    };

    // Send welcome email via Brevo (professional service)
    const brevoService = BrevoEmailService.getInstance();
    const emailResult = await brevoService.sendWelcomeEmailWithPDF({
      name: enrichedLeadData.name,
      email: enrichedLeadData.email,
      source: enrichedLeadData.source
    });

    // Track conversion event
    trackConversion(enrichedLeadData);

    return {
      success: true,
      message: 'Lead capturado com sucesso! Verifique seu email.',
      data: {
        leadId: `brevo_${Date.now()}`,
        affiliateLink: 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368',
        emailSent: emailResult.success
      }
    };

  } catch (error) {
    console.error('Lead capture error:', error);
    
    return {
      success: false,
      message: 'Erro ao processar solicitação. Tente novamente.',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

// Note: Email sending is now handled by Brevo service directly in the main function

// Track conversion for analytics
function trackConversion(leadData: LeadData): void {
  try {
    // Google Analytics 4 Event
    if (typeof gtag !== 'undefined') {
      gtag('event', 'lead_capture', {
        event_category: 'conversion',
        event_label: leadData.source,
        value: 1
      });
    }

    // Facebook Pixel Event
    if (typeof fbq !== 'undefined') {
      fbq('track', 'Lead', {
        content_name: 'AffiliateFlow Premium',
        content_category: 'Lead Generation',
        value: 1,
        currency: 'BRL'
      });
    }

    // Custom event for internal tracking
    window.dispatchEvent(new CustomEvent('leadCaptured', {
      detail: {
        email: leadData.email,
        source: leadData.source,
        timestamp: new Date().toISOString()
      }
    }));

  } catch (error) {
    console.error('Tracking error:', error);
  }
}

// Validate email format
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate phone format (Brazilian)
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^(\+55\s?)?(\(?\d{2}\)?\s?)?\d{4,5}-?\d{4}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

// Format phone number
export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (cleaned.length === 10) {
    return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  
  return phone;
}

// Get user's location for better targeting
export async function getUserLocation(): Promise<{ country?: string; region?: string; city?: string }> {
  try {
    const response = await fetch('https://ipapi.co/json/');
    const data = await response.json();
    
    return {
      country: data.country_name,
      region: data.region,
      city: data.city
    };
  } catch (error) {
    console.error('Location detection error:', error);
    return {};
  }
}
