// ===================================================================
// SISTEMA AVANÇADO DE FUNIL DE MARKETING - ARQUITETURA ROBUSTA
// Sistema completo com múltiplos provedores e fallback automático
// ===================================================================

export interface ILead {
  id: string;
  name: string;
  email: string;
  phone?: string;
  source: string;
  stage: FunnelStage;
  score: number;
  tags: string[];
  createdAt: Date;
  lastActivity: Date;
  conversions: IConversion[];
}

export interface IConversion {
  id: string;
  type: ConversionType;
  value: number;
  timestamp: Date;
  source: string;
}

export type FunnelStage = 
  | 'awareness'      // Conhecimento - Topo do funil
  | 'interest'       // Interesse - Meio do funil
  | 'consideration'  // Consideração - Meio do funil
  | 'intent'         // Intenção - Fundo do funil
  | 'purchase'       // Compra - Conversão
  | 'retention'      // Retenção - Pós-venda
  | 'advocacy';      // Advocacia - Embaixadores

export type ConversionType = 
  | 'lead_capture'
  | 'email_open'
  | 'email_click'
  | 'page_visit'
  | 'download'
  | 'registration'
  | 'purchase'
  | 'referral';

export type EmailProvider = 'brevo' | 'emailjs' | 'formspree' | 'webhook';

export interface IEmailConfig {
  provider: EmailProvider;
  apiKey?: string;
  serviceId?: string;
  templateId?: string;
  endpoint?: string;
  priority: number;
  active: boolean;
}

export interface ICampaign {
  id: string;
  name: string;
  type: 'welcome' | 'nurture' | 'conversion' | 'retention';
  trigger: 'immediate' | 'delayed' | 'behavioral';
  delay?: number; // em horas
  conditions: ICampaignCondition[];
  template: IEmailTemplate;
  active: boolean;
}

export interface ICampaignCondition {
  field: keyof ILead;
  operator: 'equals' | 'contains' | 'greater' | 'less';
  value: any;
}

export interface IEmailTemplate {
  subject: string;
  htmlContent: string;
  textContent?: string;
  attachments?: IAttachment[];
}

export interface IAttachment {
  name: string;
  content: string; // base64
  type: string;
}

// ===================================================================
// CLASSE PRINCIPAL DO FUNIL DE MARKETING
// ===================================================================

export class MarketingFunnelSystem {
  private static instance: MarketingFunnelSystem;
  private emailConfigs: IEmailConfig[] = [];
  private campaigns: ICampaign[] = [];
  private leads: Map<string, ILead> = new Map();

  private constructor() {
    this.initializeEmailProviders();
    this.initializeCampaigns();
    this.loadLeadsFromStorage();
  }

  public static getInstance(): MarketingFunnelSystem {
    if (!MarketingFunnelSystem.instance) {
      MarketingFunnelSystem.instance = new MarketingFunnelSystem();
    }
    return MarketingFunnelSystem.instance;
  }

  // ===================================================================
  // CONFIGURAÇÃO DOS PROVEDORES DE EMAIL
  // ===================================================================

  private initializeEmailProviders(): void {
    this.emailConfigs = [
      {
        provider: 'brevo',
        apiKey: '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',
        priority: 1,
        active: true
      },
      {
        provider: 'emailjs',
        serviceId: 'service_affiliate',
        templateId: 'template_welcome',
        apiKey: 'user_emailjs_key',
        priority: 2,
        active: true
      },
      {
        provider: 'formspree',
        endpoint: 'https://formspree.io/f/your-form-id',
        priority: 3,
        active: true
      },
      {
        provider: 'webhook',
        endpoint: 'https://hook.eu1.make.com/your-webhook',
        priority: 4,
        active: true
      }
    ];
  }

  // ===================================================================
  // CONFIGURAÇÃO DAS CAMPANHAS AUTOMATIZADAS
  // ===================================================================

  private initializeCampaigns(): void {
    this.campaigns = [
      {
        id: 'welcome_immediate',
        name: 'Boas-vindas Imediato',
        type: 'welcome',
        trigger: 'immediate',
        conditions: [
          { field: 'stage', operator: 'equals', value: 'awareness' }
        ],
        template: this.getWelcomeTemplate(),
        active: true
      },
      {
        id: 'nurture_day1',
        name: 'Nurturing Dia 1',
        type: 'nurture',
        trigger: 'delayed',
        delay: 24,
        conditions: [
          { field: 'stage', operator: 'equals', value: 'interest' }
        ],
        template: this.getNurtureTemplate1(),
        active: true
      },
      {
        id: 'conversion_day3',
        name: 'Conversão Dia 3',
        type: 'conversion',
        trigger: 'delayed',
        delay: 72,
        conditions: [
          { field: 'stage', operator: 'equals', value: 'consideration' }
        ],
        template: this.getConversionTemplate(),
        active: true
      }
    ];
  }

  // ===================================================================
  // CAPTURA E PROCESSAMENTO DE LEADS
  // ===================================================================

  public async captureLead(data: {
    name: string;
    email: string;
    phone?: string;
    source: string;
  }): Promise<{ success: boolean; leadId?: string; error?: string }> {
    try {
      console.log('🎯 FUNIL: Iniciando captura de lead avançada');

      // Criar lead com scoring inicial
      const lead: ILead = {
        id: `lead_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: data.name,
        email: data.email.toLowerCase(),
        phone: data.phone,
        source: data.source,
        stage: 'awareness',
        score: this.calculateInitialScore(data),
        tags: this.generateInitialTags(data),
        createdAt: new Date(),
        lastActivity: new Date(),
        conversions: []
      };

      // Salvar lead
      this.leads.set(lead.id, lead);
      this.saveLeadsToStorage();

      console.log('✅ Lead criado:', lead.id, 'Score:', lead.score);

      // Registrar conversão de captura
      await this.trackConversion(lead.id, 'lead_capture', 1);

      // Executar campanhas imediatas
      await this.executeCampaigns(lead, 'immediate');

      // Agendar campanhas com delay
      this.scheduleCampaigns(lead);

      return { success: true, leadId: lead.id };

    } catch (error) {
      console.error('❌ Erro na captura de lead:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Erro desconhecido' };
    }
  }

  // ===================================================================
  // SISTEMA DE SCORING DE LEADS
  // ===================================================================

  private calculateInitialScore(data: { name: string; email: string; phone?: string; source: string }): number {
    let score = 0;

    // Pontuação por completude dos dados
    if (data.name && data.name.length > 2) score += 10;
    if (data.email && data.email.includes('@')) score += 20;
    if (data.phone && data.phone.length > 8) score += 15;

    // Pontuação por fonte
    const sourceScores: Record<string, number> = {
      'organic': 25,
      'social': 20,
      'paid': 15,
      'referral': 30,
      'direct': 10
    };
    score += sourceScores[data.source] || 5;

    // Pontuação por qualidade do email
    if (data.email.includes('gmail') || data.email.includes('outlook')) score += 5;
    if (!data.email.includes('temp') && !data.email.includes('10min')) score += 10;

    return Math.min(score, 100);
  }

  private generateInitialTags(data: { source: string; email: string }): string[] {
    const tags: string[] = ['new_lead'];

    // Tags por fonte
    tags.push(`source_${data.source}`);

    // Tags por domínio de email
    const domain = data.email.split('@')[1];
    if (domain.includes('gmail')) tags.push('gmail_user');
    if (domain.includes('outlook') || domain.includes('hotmail')) tags.push('microsoft_user');
    if (domain.includes('yahoo')) tags.push('yahoo_user');

    // Tags por comportamento inferido
    if (data.source === 'organic') tags.push('high_intent');
    if (data.source === 'social') tags.push('social_active');

    return tags;
  }

  // ===================================================================
  // SISTEMA DE ENVIO DE EMAILS COM FALLBACK
  // ===================================================================

  private async sendEmailWithFallback(
    lead: ILead,
    template: IEmailTemplate
  ): Promise<{ success: boolean; provider?: string; error?: string }> {

    // Ordenar provedores por prioridade
    const activeProviders = this.emailConfigs
      .filter(config => config.active)
      .sort((a, b) => a.priority - b.priority);

    for (const config of activeProviders) {
      try {
        console.log(`📧 Tentando envio via ${config.provider}...`);

        const result = await this.sendViaProvider(config, lead, template);

        if (result.success) {
          console.log(`✅ Email enviado com sucesso via ${config.provider}`);
          await this.trackConversion(lead.id, 'email_open', 1);
          return { success: true, provider: config.provider };
        }

      } catch (error) {
        console.log(`❌ Falha no ${config.provider}:`, error);
        continue;
      }
    }

    return { success: false, error: 'Todos os provedores falharam' };
  }

  private async sendViaProvider(
    config: IEmailConfig,
    lead: ILead,
    template: IEmailTemplate
  ): Promise<{ success: boolean; error?: string }> {

    switch (config.provider) {
      case 'brevo':
        return this.sendViaBrevo(config, lead, template);

      case 'emailjs':
        return this.sendViaEmailJS(config, lead, template);

      case 'formspree':
        return this.sendViaFormspree(config, lead, template);

      case 'webhook':
        return this.sendViaWebhook(config, lead, template);

      default:
        return { success: false, error: 'Provedor não suportado' };
    }
  }

  private async sendViaBrevo(
    config: IEmailConfig,
    lead: ILead,
    template: IEmailTemplate
  ): Promise<{ success: boolean; error?: string }> {

    try {
      const response = await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': config.apiKey!
        },
        body: JSON.stringify({
          sender: {
            email: '<EMAIL>',
            name: 'AffiliateFlow Pro'
          },
          to: [{
            email: lead.email,
            name: lead.name
          }],
          subject: template.subject,
          htmlContent: template.htmlContent,
          tags: ['funnel', lead.stage, ...lead.tags]
        })
      });

      if (response.ok) {
        return { success: true };
      } else {
        const error = await response.text();
        return { success: false, error };
      }

    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Erro Brevo' };
    }
  }

  private async sendViaEmailJS(
    config: IEmailConfig,
    lead: ILead,
    template: IEmailTemplate
  ): Promise<{ success: boolean; error?: string }> {

    try {
      // Implementação EmailJS (requer biblioteca)
      console.log('📧 EmailJS não implementado nesta versão');
      return { success: false, error: 'EmailJS não configurado' };

    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Erro EmailJS' };
    }
  }

  private async sendViaFormspree(
    config: IEmailConfig,
    lead: ILead,
    template: IEmailTemplate
  ): Promise<{ success: boolean; error?: string }> {

    try {
      const response = await fetch(config.endpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: lead.email,
          name: lead.name,
          subject: template.subject,
          message: template.htmlContent,
          _replyto: lead.email
        })
      });

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, error: 'Formspree falhou' };
      }

    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Erro Formspree' };
    }
  }

  private async sendViaWebhook(
    config: IEmailConfig,
    lead: ILead,
    template: IEmailTemplate
  ): Promise<{ success: boolean; error?: string }> {

    try {
      const response = await fetch(config.endpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          lead: lead,
          template: template,
          timestamp: new Date().toISOString()
        })
      });

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, error: 'Webhook falhou' };
      }

    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Erro Webhook' };
    }
  }

  // ===================================================================
  // TEMPLATES DE EMAIL ESTRATÉGICOS
  // ===================================================================

  private getWelcomeTemplate(): IEmailTemplate {
    return {
      subject: '🎉 Bem-vindo ao Programa GRIP - Sua Jornada para R$ 15k/mês Começa Agora!',
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Bem-vindo ao AffiliateFlow Pro</title>
        </head>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; background: #f8f9fa;">

          <!-- Header -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; border-radius: 15px 15px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 32px; font-weight: bold;">🎉 Parabéns, {{FIRST_NAME}}!</h1>
            <p style="color: rgba(255,255,255,0.9); margin: 15px 0 0 0; font-size: 18px;">Você agora faz parte do seleto grupo que gera <strong>RENDA RECORRENTE</strong> indicando IA para empresas!</p>
          </div>

          <!-- Conteúdo Principal -->
          <div style="background: white; padding: 40px 30px;">

            <!-- Sobre a GRIP -->
            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 15px; padding: 25px; margin-bottom: 30px; color: white;">
              <h2 style="margin-top: 0; font-size: 24px;">🤖 SOBRE A GRIP - PLATAFORMA DE IA EMPRESARIAL</h2>
              <ul style="list-style: none; padding: 0; margin: 0;">
                <li style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                  ✅ <strong>Soluções de IA financeira</strong> para empresas de todos os portes
                </li>
                <li style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                  ✅ <strong>Planos de R$ 15.000/mês</strong> até R$ 55.000 (Enterprise)
                </li>
                <li style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                  ✅ <strong>Comissões recorrentes</strong> para afiliados qualificados
                </li>
                <li style="margin: 12px 0; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                  ✅ <strong>Suporte técnico especializado</strong> 24/7
                </li>
              </ul>
            </div>

            <!-- Potencial de Ganhos -->
            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 15px; padding: 25px; margin-bottom: 30px; color: white;">
              <h2 style="margin-top: 0; font-size: 24px;">💰 SEU POTENCIAL DE GANHOS</h2>
              <div style="display: grid; gap: 15px;">
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                  <strong style="font-size: 18px;">Base: R$ 15.000/mês</strong><br>
                  <span style="opacity: 0.9;">Por cada empresa que você indicar e fechar contrato</span>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                  <strong style="font-size: 18px;">Enterprise: R$ 55.000</strong><br>
                  <span style="opacity: 0.9;">Quando empresa contrata plano premium</span>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                  <strong style="font-size: 18px;">Escalável: Sem Limite</strong><br>
                  <span style="opacity: 0.9;">Quanto mais empresas, maior sua renda mensal</span>
                </div>
              </div>
            </div>

            <!-- Call to Action Principal -->
            <div style="text-align: center; margin: 40px 0;">
              <h2 style="color: #2c3e50; margin-bottom: 20px;">🔗 SEUS PRÓXIMOS PASSOS</h2>

              <div style="margin-bottom: 20px;">
                <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368"
                   style="display: inline-block; background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 18px 35px; text-decoration: none; border-radius: 12px; font-weight: bold; font-size: 18px; box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);">
                  🚀 1. ACESSAR SEU LINK DE AFILIADO
                </a>
              </div>

              <p style="color: #666; margin: 20px 0;">Baixe o app GRIP para acompanhar suas indicações:</p>

              <div style="display: inline-block;">
                <a href="https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share"
                   style="display: inline-block; background: #34A853; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 0 10px 10px 0; font-weight: bold;">
                  📱 Android
                </a>
                <a href="https://apps.apple.com/us/app/grip-gaiodataos/id6743857628"
                   style="display: inline-block; background: #007AFF; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 0 0 10px 10px; font-weight: bold;">
                  📱 iOS
                </a>
              </div>
            </div>

            <!-- Próximos Emails -->
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; margin: 30px 0;">
              <h3 style="color: #856404; margin-top: 0;">📅 O QUE VEM POR AÍ</h3>
              <ul style="color: #856404; margin: 0; padding-left: 20px;">
                <li style="margin: 8px 0;"><strong>Amanhã:</strong> Estratégias para encontrar empresas interessadas</li>
                <li style="margin: 8px 0;"><strong>Em 3 dias:</strong> Scripts de abordagem que convertem</li>
                <li style="margin: 8px 0;"><strong>Em 1 semana:</strong> Como escalar para R$ 50k/mês</li>
              </ul>
            </div>

          </div>

          <!-- Footer -->
          <div style="background: #2c3e50; color: white; padding: 30px; text-align: center; border-radius: 0 0 15px 15px;">
            <p style="margin: 0; font-size: 16px;">
              Sucesso e bons ganhos! 💪<br>
              <strong>Equipe AffiliateFlow Pro</strong>
            </p>
            <p style="margin: 15px 0 0 0; font-size: 12px; opacity: 0.7;">
              Você está recebendo este email porque se cadastrou em nosso programa de afiliados.
            </p>
          </div>

        </body>
        </html>
      `
    };
  }

  private getNurtureTemplate1(): IEmailTemplate {
    return {
      subject: '💡 Como Identificar Empresas que Precisam de IA (+ Lista Exclusiva)',
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; background: #f8f9fa;">

          <div style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); padding: 30px 20px; text-align: center; border-radius: 15px 15px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">💡 Dia 1: Encontrando Seus Primeiros Clientes</h1>
            <p style="color: rgba(255,255,255,0.9); margin: 15px 0 0 0; font-size: 16px;">Estratégias comprovadas para identificar empresas que precisam de IA</p>
          </div>

          <div style="background: white; padding: 30px;">

            <h2 style="color: #2c3e50;">🎯 PERFIL IDEAL DO CLIENTE GRIP</h2>
            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0; border-radius: 0 10px 10px 0;">
              <ul style="margin: 0; padding-left: 20px;">
                <li><strong>Empresas de médio/grande porte</strong> (50+ funcionários)</li>
                <li><strong>Setor financeiro, contábil, consultoria</strong></li>
                <li><strong>Faturamento anual acima de R$ 5 milhões</strong></li>
                <li><strong>Já usam tecnologia</strong> no dia a dia</li>
                <li><strong>Buscam otimização de processos</strong></li>
              </ul>
            </div>

            <h2 style="color: #2c3e50;">🔍 ONDE ENCONTRAR ESSES CLIENTES</h2>
            <div style="display: grid; gap: 15px; margin: 20px 0;">
              <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; border-left: 4px solid #007AFF;">
                <strong>LinkedIn:</strong> Busque por "CFO", "Diretor Financeiro", "Controller"
              </div>
              <div style="background: #fff0f5; padding: 15px; border-radius: 10px; border-left: 4px solid #ff69b4;">
                <strong>Eventos:</strong> Congressos de contabilidade, feiras de tecnologia
              </div>
              <div style="background: #f0fff0; padding: 15px; border-radius: 10px; border-left: 4px solid #32cd32;">
                <strong>Indicações:</strong> Contadores, consultores, advogados empresariais
              </div>
            </div>

            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;">
              <h3 style="margin-top: 0;">🎁 BÔNUS EXCLUSIVO</h3>
              <p style="margin: 15px 0;">Lista com 100 empresas pré-qualificadas que podem se interessar pela GRIP</p>
              <a href="#" style="display: inline-block; background: white; color: #667eea; padding: 12px 25px; text-decoration: none; border-radius: 8px; font-weight: bold; margin-top: 10px;">
                📋 BAIXAR LISTA AGORA
              </a>
            </div>

            <h2 style="color: #2c3e50;">💬 SCRIPT DE PRIMEIRA ABORDAGEM</h2>
            <div style="background: #fffacd; border: 1px solid #ffd700; padding: 20px; border-radius: 10px; margin: 20px 0;">
              <p style="margin: 0; font-style: italic;">
                "Olá [Nome], vi que você é [Cargo] na [Empresa]. Vocês já avaliaram como a IA pode otimizar os processos financeiros da empresa? Tenho uma solução que está gerando economia de 40% nos custos operacionais para empresas similares. Posso compartilhar um case rápido?"
              </p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368"
                 style="display: inline-block; background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold;">
                🚀 ACESSAR PLATAFORMA GRIP
              </a>
            </div>

          </div>

          <div style="background: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 0 0 15px 15px;">
            <p style="margin: 0;">Amanhã: <strong>Scripts de conversão que fecham negócios</strong> 🎯</p>
          </div>

        </body>
        </html>
      `
    };
  }

  private getConversionTemplate(): IEmailTemplate {
    return {
      subject: '🔥 URGENTE: Últimas 48h para Garantir Sua Vaga no Programa Elite',
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; background: #f8f9fa;">

          <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); padding: 30px 20px; text-align: center; border-radius: 15px 15px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🔥 ÚLTIMAS 48 HORAS!</h1>
            <p style="color: rgba(255,255,255,0.9); margin: 15px 0 0 0; font-size: 18px;">Sua vaga no Programa Elite GRIP expira em breve</p>
          </div>

          <div style="background: white; padding: 30px;">

            <div style="background: #ffebee; border: 2px solid #f44336; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
              <h2 style="color: #d32f2f; margin-top: 0;">⏰ TEMPO LIMITADO</h2>
              <p style="margin: 0; font-size: 16px; color: #666;">
                Apenas <strong>12 vagas restantes</strong> para afiliados que querem ganhar R$ 15k-55k/mês
              </p>
            </div>

            <h2 style="color: #2c3e50;">🏆 O QUE VOCÊ GANHA NO PROGRAMA ELITE:</h2>
            <div style="display: grid; gap: 15px; margin: 20px 0;">
              <div style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px; border-radius: 10px;">
                ✅ <strong>Comissões 50% maiores</strong> nos primeiros 6 meses
              </div>
              <div style="background: linear-gradient(135deg, #2196F3, #1976D2); color: white; padding: 15px; border-radius: 10px;">
                ✅ <strong>Suporte dedicado</strong> para suas indicações
              </div>
              <div style="background: linear-gradient(135deg, #FF9800, #F57C00); color: white; padding: 15px; border-radius: 10px;">
                ✅ <strong>Material exclusivo</strong> de vendas
              </div>
              <div style="background: linear-gradient(135deg, #9C27B0, #7B1FA2); color: white; padding: 15px; border-radius: 10px;">
                ✅ <strong>Treinamento VIP</strong> com especialistas
              </div>
            </div>

            <div style="background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%); color: white; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;">
              <h3 style="margin-top: 0;">💰 POTENCIAL REAL DE GANHOS</h3>
              <div style="display: grid; gap: 10px; margin: 15px 0;">
                <div>1 empresa/mês = <strong>R$ 15.000</strong></div>
                <div>3 empresas/mês = <strong>R$ 45.000</strong></div>
                <div>5 empresas/mês = <strong>R$ 75.000</strong></div>
                <div style="font-size: 18px; margin-top: 10px;">+ Bônus Enterprise até <strong>R$ 55.000</strong></div>
              </div>
            </div>

            <div style="text-align: center; margin: 40px 0;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">🚨 AÇÃO NECESSÁRIA AGORA</h2>

              <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368"
                 style="display: inline-block; background: linear-gradient(135deg, #f44336, #d32f2f); color: white; padding: 20px 40px; text-decoration: none; border-radius: 12px; font-weight: bold; font-size: 18px; box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3); animation: pulse 2s infinite;">
                🔥 GARANTIR MINHA VAGA ELITE
              </a>

              <p style="color: #666; margin: 20px 0; font-size: 14px;">
                ⏰ Oferta expira em 48 horas | Apenas 12 vagas restantes
              </p>
            </div>

            <div style="background: #e8f5e8; border-left: 4px solid #4CAF50; padding: 20px; margin: 20px 0;">
              <h3 style="color: #2e7d32; margin-top: 0;">✅ GARANTIA TOTAL</h3>
              <p style="margin: 0; color: #666;">
                Se você não fizer sua primeira indicação em 30 dias, devolvemos 100% do investimento. Sem perguntas.
              </p>
            </div>

          </div>

          <div style="background: #d32f2f; color: white; padding: 20px; text-align: center; border-radius: 0 0 15px 15px;">
            <p style="margin: 0; font-weight: bold;">⏰ Restam apenas 48 horas para garantir sua vaga!</p>
          </div>

        </body>
        </html>
      `
    };
  }

  // ===================================================================
  // EXECUÇÃO E AGENDAMENTO DE CAMPANHAS
  // ===================================================================

  private async executeCampaigns(lead: ILead, trigger: 'immediate' | 'delayed' | 'behavioral'): Promise<void> {
    const applicableCampaigns = this.campaigns.filter(campaign =>
      campaign.active &&
      campaign.trigger === trigger &&
      this.checkCampaignConditions(lead, campaign.conditions)
    );

    for (const campaign of applicableCampaigns) {
      console.log(`📧 Executando campanha: ${campaign.name}`);

      const template = this.personalizeTemplate(campaign.template, lead);
      const result = await this.sendEmailWithFallback(lead, template);

      if (result.success) {
        console.log(`✅ Campanha ${campaign.name} enviada via ${result.provider}`);
      } else {
        console.log(`❌ Falha na campanha ${campaign.name}: ${result.error}`);
      }
    }
  }

  private scheduleCampaigns(lead: ILead): void {
    const delayedCampaigns = this.campaigns.filter(campaign =>
      campaign.active &&
      campaign.trigger === 'delayed' &&
      this.checkCampaignConditions(lead, campaign.conditions)
    );

    for (const campaign of delayedCampaigns) {
      const delayMs = (campaign.delay || 24) * 60 * 60 * 1000; // horas para ms

      setTimeout(async () => {
        console.log(`⏰ Executando campanha agendada: ${campaign.name}`);
        await this.executeCampaigns(lead, 'delayed');
      }, delayMs);
    }
  }

  private checkCampaignConditions(lead: ILead, conditions: ICampaignCondition[]): boolean {
    return conditions.every(condition => {
      const leadValue = lead[condition.field];

      switch (condition.operator) {
        case 'equals':
          return leadValue === condition.value;
        case 'contains':
          return String(leadValue).includes(condition.value);
        case 'greater':
          return Number(leadValue) > Number(condition.value);
        case 'less':
          return Number(leadValue) < Number(condition.value);
        default:
          return false;
      }
    });
  }

  private personalizeTemplate(template: IEmailTemplate, lead: ILead): IEmailTemplate {
    const firstName = lead.name.split(' ')[0];

    return {
      ...template,
      subject: template.subject.replace('{{FIRST_NAME}}', firstName),
      htmlContent: template.htmlContent
        .replace(/{{FIRST_NAME}}/g, firstName)
        .replace(/{{FULL_NAME}}/g, lead.name)
        .replace(/{{EMAIL}}/g, lead.email)
    };
  }

  // ===================================================================
  // TRACKING E ANALYTICS
  // ===================================================================

  public async trackConversion(
    leadId: string,
    type: ConversionType,
    value: number = 1
  ): Promise<void> {
    const lead = this.leads.get(leadId);
    if (!lead) return;

    const conversion: IConversion = {
      id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      value,
      timestamp: new Date(),
      source: lead.source
    };

    lead.conversions.push(conversion);
    lead.lastActivity = new Date();

    // Atualizar score baseado na conversão
    lead.score += this.getConversionScore(type);

    // Atualizar estágio do funil se necessário
    this.updateLeadStage(lead, type);

    this.leads.set(leadId, lead);
    this.saveLeadsToStorage();

    console.log(`📊 Conversão registrada: ${type} para lead ${leadId}`);
  }

  private getConversionScore(type: ConversionType): number {
    const scores: Record<ConversionType, number> = {
      'lead_capture': 10,
      'email_open': 5,
      'email_click': 15,
      'page_visit': 8,
      'download': 20,
      'registration': 30,
      'purchase': 100,
      'referral': 50
    };
    return scores[type] || 0;
  }

  private updateLeadStage(lead: ILead, conversionType: ConversionType): void {
    const stageProgression: Record<ConversionType, FunnelStage> = {
      'lead_capture': 'awareness',
      'email_open': 'interest',
      'email_click': 'interest',
      'page_visit': 'consideration',
      'download': 'consideration',
      'registration': 'intent',
      'purchase': 'purchase',
      'referral': 'advocacy'
    };

    const newStage = stageProgression[conversionType];
    if (newStage && this.isStageProgression(lead.stage, newStage)) {
      lead.stage = newStage;
    }
  }

  private isStageProgression(currentStage: FunnelStage, newStage: FunnelStage): boolean {
    const stageOrder: FunnelStage[] = [
      'awareness', 'interest', 'consideration', 'intent', 'purchase', 'retention', 'advocacy'
    ];

    const currentIndex = stageOrder.indexOf(currentStage);
    const newIndex = stageOrder.indexOf(newStage);

    return newIndex > currentIndex;
  }

  // ===================================================================
  // PERSISTÊNCIA DE DADOS
  // ===================================================================

  private saveLeadsToStorage(): void {
    try {
      const leadsArray = Array.from(this.leads.values());
      localStorage.setItem('marketing_funnel_leads', JSON.stringify(leadsArray));
    } catch (error) {
      console.error('Erro ao salvar leads:', error);
    }
  }

  private loadLeadsFromStorage(): void {
    try {
      const stored = localStorage.getItem('marketing_funnel_leads');
      if (stored) {
        const leadsArray: ILead[] = JSON.parse(stored);
        leadsArray.forEach(lead => {
          // Converter strings de data de volta para objetos Date
          lead.createdAt = new Date(lead.createdAt);
          lead.lastActivity = new Date(lead.lastActivity);
          lead.conversions.forEach(conv => {
            conv.timestamp = new Date(conv.timestamp);
          });
          this.leads.set(lead.id, lead);
        });
      }
    } catch (error) {
      console.error('Erro ao carregar leads:', error);
    }
  }

  // ===================================================================
  // MÉTODOS PÚBLICOS PARA ANALYTICS
  // ===================================================================

  public getLeadsByStage(): Record<FunnelStage, number> {
    const stages: Record<FunnelStage, number> = {
      awareness: 0,
      interest: 0,
      consideration: 0,
      intent: 0,
      purchase: 0,
      retention: 0,
      advocacy: 0
    };

    Array.from(this.leads.values()).forEach(lead => {
      stages[lead.stage]++;
    });

    return stages;
  }

  public getConversionRate(): number {
    const totalLeads = this.leads.size;
    const convertedLeads = Array.from(this.leads.values())
      .filter(lead => lead.stage === 'purchase' || lead.stage === 'retention' || lead.stage === 'advocacy')
      .length;

    return totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;
  }

  public getTopSources(): Array<{ source: string; count: number; conversionRate: number }> {
    const sources: Record<string, { count: number; converted: number }> = {};

    Array.from(this.leads.values()).forEach(lead => {
      if (!sources[lead.source]) {
        sources[lead.source] = { count: 0, converted: 0 };
      }
      sources[lead.source].count++;

      if (['purchase', 'retention', 'advocacy'].includes(lead.stage)) {
        sources[lead.source].converted++;
      }
    });

    return Object.entries(sources)
      .map(([source, data]) => ({
        source,
        count: data.count,
        conversionRate: data.count > 0 ? (data.converted / data.count) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count);
  }
}
