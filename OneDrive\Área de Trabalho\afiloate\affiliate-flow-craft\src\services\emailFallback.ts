// ===================================================================
// EMAIL FALLBACK SERVICE - AffiliateFlow Pro
// Fallback system when primary email service is not configured
// ===================================================================

import { createLogger } from '../core/utils/logger';
import { config } from '../core/config';
import { ILead, IApiResponse } from '../core/types';

const fallbackLogger = createLogger('EMAIL_FALLBACK');

// Simple email templates for fallback
const FALLBACK_TEMPLATES = {
  welcome: {
    subject: '🎉 Bem-vindo ao AffiliateFlow Pro!',
    body: `O<PERSON><PERSON> {{name}},

Parabéns! Você agora faz parte do programa de afiliados GRIP!

🤖 Sobre a GRIP:
• Plataforma de IA financeira empresarial
• Comissões de R$ 15.000/mês por empresa
• Até R$ 55.000 no plano Enterprise

🔗 Seu link de afiliado: {{affiliateLink}}

Em breve você receberá materiais exclusivos para seu nicho!

Sucesso!
Equipe AffiliateFlow Pro`
  }
};

class EmailFallbackService {
  private static instance: EmailFallbackService;

  private constructor() {
    fallbackLogger.info('Email Fallback Service initialized');
  }

  public static getInstance(): EmailFallbackService {
    if (!EmailFallbackService.instance) {
      EmailFallbackService.instance = new EmailFallbackService();
    }
    return EmailFallbackService.instance;
  }

  public async sendWelcomeEmail(lead: ILead): Promise<IApiResponse> {
    fallbackLogger.info('Sending fallback welcome email', {
      leadId: lead.id,
      email: lead.email,
      niche: lead.niche
    });

    try {
      // In a real fallback, this could use:
      // 1. EmailJS as backup
      // 2. Simple SMTP
      // 3. Webhook to external service
      // 4. Queue for later processing

      // For now, we'll simulate success and log the email
      const emailContent = this.processTemplate(FALLBACK_TEMPLATES.welcome, lead);
      
      // Log the email that would be sent
      fallbackLogger.info('Fallback email content generated', {
        to: lead.email,
        subject: FALLBACK_TEMPLATES.welcome.subject,
        contentLength: emailContent.length
      });

      // Store in localStorage for demo purposes
      this.storeEmailForDemo(lead, emailContent);

      return {
        success: true,
        data: {
          message: 'Email enviado via sistema de fallback',
          emailId: `fallback_${Date.now()}`,
          method: 'fallback'
        },
        timestamp: new Date(),
        requestId: `fallback_${Math.random().toString(36).substr(2, 8)}`
      };

    } catch (error) {
      fallbackLogger.error('Fallback email failed', {
        leadId: lead.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        error: {
          code: 'FALLBACK_FAILED',
          message: 'Sistema de fallback falhou'
        },
        timestamp: new Date(),
        requestId: `fallback_error_${Math.random().toString(36).substr(2, 8)}`
      };
    }
  }

  private processTemplate(template: { subject: string; body: string }, lead: ILead): string {
    let content = template.body;
    
    const replacements = {
      name: lead.name,
      email: lead.email,
      affiliateLink: lead.affiliateLink,
      niche: lead.niche
    };

    Object.entries(replacements).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      content = content.replace(regex, value || '');
    });

    return content;
  }

  private storeEmailForDemo(lead: ILead, content: string): void {
    try {
      const emails = JSON.parse(localStorage.getItem('demo_emails') || '[]');
      emails.push({
        id: `demo_${Date.now()}`,
        to: lead.email,
        subject: FALLBACK_TEMPLATES.welcome.subject,
        content,
        timestamp: new Date().toISOString(),
        method: 'fallback'
      });

      // Keep only last 10 emails
      if (emails.length > 10) {
        emails.splice(0, emails.length - 10);
      }

      localStorage.setItem('demo_emails', JSON.stringify(emails));
      
      fallbackLogger.debug('Email stored for demo', {
        emailId: emails[emails.length - 1].id,
        totalEmails: emails.length
      });
    } catch (error) {
      fallbackLogger.warn('Failed to store demo email', { error });
    }
  }

  public getDemoEmails(): any[] {
    try {
      return JSON.parse(localStorage.getItem('demo_emails') || '[]');
    } catch {
      return [];
    }
  }

  public clearDemoEmails(): void {
    localStorage.removeItem('demo_emails');
    fallbackLogger.info('Demo emails cleared');
  }
}

// Export singleton instance
export const emailFallback = EmailFallbackService.getInstance();
